// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VanRoute _$VanRouteFromJson(Map<String, dynamic> json) => VanRoute(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      code: json['code'] as String,
      plannedDate: DateTime.parse(json['plannedDate'] as String),
      state: $enumDecodeNullable(_$RouteStateEnumMap, json['state']) ??
          RouteState.draft,
      vehicleId: (json['vehicleId'] as num).toInt(),
      vehicleName: json['vehicleName'] as String,
      driverName: json['driverName'] as String?,
      startLocation: json['startLocation'] as String?,
      startLatitude: (json['startLatitude'] as num?)?.toDouble(),
      startLongitude: (json['startLongitude'] as num?)?.toDouble(),
      endLocation: json['endLocation'] as String?,
      endLatitude: (json['endLatitude'] as num?)?.toDouble(),
      endLongitude: (json['endLongitude'] as num?)?.toDouble(),
      totalDistance: (json['totalDistance'] as num?)?.toDouble() ?? 0.0,
      estimatedDuration: (json['estimatedDuration'] as num?)?.toDouble() ?? 0.0,
      actualDistance: (json['actualDistance'] as num?)?.toDouble(),
      actualDuration: (json['actualDuration'] as num?)?.toDouble(),
      visits: (json['visits'] as List<dynamic>?)
              ?.map((e) => Visit.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      notes: json['notes'] as String?,
      specialInstructions: json['specialInstructions'] as String?,
      startTime: json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      optimizedRoute: json['optimizedRoute'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$VanRouteToJson(VanRoute instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'plannedDate': instance.plannedDate.toIso8601String(),
      'state': _$RouteStateEnumMap[instance.state]!,
      'vehicleId': instance.vehicleId,
      'vehicleName': instance.vehicleName,
      'driverName': instance.driverName,
      'startLocation': instance.startLocation,
      'startLatitude': instance.startLatitude,
      'startLongitude': instance.startLongitude,
      'endLocation': instance.endLocation,
      'endLatitude': instance.endLatitude,
      'endLongitude': instance.endLongitude,
      'totalDistance': instance.totalDistance,
      'estimatedDuration': instance.estimatedDuration,
      'actualDistance': instance.actualDistance,
      'actualDuration': instance.actualDuration,
      'visits': instance.visits,
      'notes': instance.notes,
      'specialInstructions': instance.specialInstructions,
      'startTime': instance.startTime?.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'optimizedRoute': instance.optimizedRoute,
    };

const _$RouteStateEnumMap = {
  RouteState.draft: 'draft',
  RouteState.planned: 'planned',
  RouteState.inProgress: 'inProgress',
  RouteState.completed: 'completed',
  RouteState.cancelled: 'cancelled',
};
