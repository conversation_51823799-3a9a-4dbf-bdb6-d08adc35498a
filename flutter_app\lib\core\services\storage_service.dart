import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/user.dart';
import '../../models/vehicle.dart';
import '../../models/route.dart';
import '../../models/customer.dart';
import '../../models/visit.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static late Box<User> _userBox;
  static late Box<Vehicle> _vehicleBox;
  static late Box<VanRoute> _routeBox;
  static late Box<Customer> _customerBox;
  static late Box<Visit> _visitBox;
  static late Box<Map> _cacheBox;

  static const String _userBoxName = 'users';
  static const String _vehicleBoxName = 'vehicles';
  static const String _routeBoxName = 'routes';
  static const String _customerBoxName = 'customers';
  static const String _visitBoxName = 'visits';
  static const String _cacheBoxName = 'cache';

  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    
    // Open Hive boxes
    _userBox = await Hive.openBox<User>(_userBoxName);
    _vehicleBox = await Hive.openBox<Vehicle>(_vehicleBoxName);
    _routeBox = await Hive.openBox<VanRoute>(_routeBoxName);
    _customerBox = await Hive.openBox<Customer>(_customerBoxName);
    _visitBox = await Hive.openBox<Visit>(_visitBoxName);
    _cacheBox = await Hive.openBox<Map>(_cacheBoxName);
  }

  // User Storage
  static Future<void> saveUser(User user) async {
    await _userBox.put('current_user', user);
  }

  static User? getCurrentUser() {
    return _userBox.get('current_user');
  }

  static Future<void> clearUser() async {
    await _userBox.delete('current_user');
  }

  // Vehicle Storage
  static Future<void> saveVehicles(List<Vehicle> vehicles) async {
    await _vehicleBox.clear();
    for (final vehicle in vehicles) {
      await _vehicleBox.put(vehicle.id, vehicle);
    }
  }

  static List<Vehicle> getVehicles() {
    return _vehicleBox.values.toList();
  }

  static Vehicle? getVehicle(int id) {
    return _vehicleBox.get(id);
  }

  static Future<void> saveVehicle(Vehicle vehicle) async {
    await _vehicleBox.put(vehicle.id, vehicle);
  }

  // Route Storage
  static Future<void> saveRoutes(List<VanRoute> routes) async {
    await _routeBox.clear();
    for (final route in routes) {
      await _routeBox.put(route.id, route);
    }
  }

  static List<VanRoute> getRoutes() {
    return _routeBox.values.toList();
  }

  static VanRoute? getRoute(int id) {
    return _routeBox.get(id);
  }

  static Future<void> saveRoute(VanRoute route) async {
    await _routeBox.put(route.id, route);
  }

  static List<VanRoute> getRoutesByDate(DateTime date) {
    return _routeBox.values
        .where((route) => 
            route.plannedDate.year == date.year &&
            route.plannedDate.month == date.month &&
            route.plannedDate.day == date.day)
        .toList();
  }

  static List<VanRoute> getRoutesByVehicle(int vehicleId) {
    return _routeBox.values
        .where((route) => route.vehicleId == vehicleId)
        .toList();
  }

  // Customer Storage
  static Future<void> saveCustomers(List<Customer> customers) async {
    await _customerBox.clear();
    for (final customer in customers) {
      await _customerBox.put(customer.id, customer);
    }
  }

  static List<Customer> getCustomers() {
    return _customerBox.values.toList();
  }

  static Customer? getCustomer(int id) {
    return _customerBox.get(id);
  }

  static Future<void> saveCustomer(Customer customer) async {
    await _customerBox.put(customer.id, customer);
  }

  static List<Customer> searchCustomers(String query) {
    return _customerBox.values
        .where((customer) => 
            customer.name.toLowerCase().contains(query.toLowerCase()) ||
            (customer.address?.toLowerCase().contains(query.toLowerCase()) ?? false))
        .toList();
  }

  // Visit Storage
  static Future<void> saveVisits(List<Visit> visits) async {
    await _visitBox.clear();
    for (final visit in visits) {
      await _visitBox.put(visit.id, visit);
    }
  }

  static List<Visit> getVisits() {
    return _visitBox.values.toList();
  }

  static Visit? getVisit(int id) {
    return _visitBox.get(id);
  }

  static Future<void> saveVisit(Visit visit) async {
    await _visitBox.put(visit.id, visit);
  }

  static List<Visit> getVisitsByCustomer(int customerId) {
    return _visitBox.values
        .where((visit) => visit.customerId == customerId)
        .toList();
  }

  static List<Visit> getVisitsByDate(DateTime date) {
    return _visitBox.values
        .where((visit) => 
            visit.visitDate.year == date.year &&
            visit.visitDate.month == date.month &&
            visit.visitDate.day == date.day)
        .toList();
  }

  // Cache Storage
  static Future<void> saveToCache(String key, Map<String, dynamic> data) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    await _cacheBox.put(key, cacheData);
  }

  static Map<String, dynamic>? getFromCache(String key, {Duration? maxAge}) {
    final cacheData = _cacheBox.get(key);
    if (cacheData == null) return null;

    if (maxAge != null) {
      final timestamp = cacheData['timestamp'] as int;
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      if (DateTime.now().difference(cacheTime) > maxAge) {
        _cacheBox.delete(key);
        return null;
      }
    }

    return cacheData['data'] as Map<String, dynamic>?;
  }

  static Future<void> clearCache() async {
    await _cacheBox.clear();
  }

  // Preferences
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs.getString(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  // Sync Status
  static Future<void> setLastSyncTime(DateTime time) async {
    await _prefs.setString('last_sync_time', time.toIso8601String());
  }

  static DateTime? getLastSyncTime() {
    final timeString = _prefs.getString('last_sync_time');
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  static Future<void> setSyncInProgress(bool inProgress) async {
    await _prefs.setBool('sync_in_progress', inProgress);
  }

  static bool isSyncInProgress() {
    return _prefs.getBool('sync_in_progress') ?? false;
  }

  // Clear all data
  static Future<void> clearAllData() async {
    await _userBox.clear();
    await _vehicleBox.clear();
    await _routeBox.clear();
    await _customerBox.clear();
    await _visitBox.clear();
    await _cacheBox.clear();
    await _prefs.clear();
  }

  // Database statistics
  static Map<String, int> getStorageStats() {
    return {
      'users': _userBox.length,
      'vehicles': _vehicleBox.length,
      'routes': _routeBox.length,
      'customers': _customerBox.length,
      'visits': _visitBox.length,
      'cache_entries': _cacheBox.length,
    };
  }
}
