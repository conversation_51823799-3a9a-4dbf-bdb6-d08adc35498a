from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class VanVehicle(models.Model):
    _name = 'van.vehicle'
    _description = 'Van Vehicle'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Vehicle Name',
        required=True,
        tracking=True
    )
    license_plate = fields.Char(
        string='License Plate',
        required=True,
        tracking=True
    )
    driver_id = fields.Many2one(
        'res.users',
        string='Driver',
        tracking=True
    )
    fleet_vehicle_id = fields.Many2one(
        'fleet.vehicle',
        string='Fleet Vehicle',
        help='Link to fleet management vehicle'
    )
    
    # GPS and Location
    current_latitude = fields.Float(
        string='Current Latitude',
        digits=(10, 6)
    )
    current_longitude = fields.Float(
        string='Current Longitude',
        digits=(10, 6)
    )
    last_location_update = fields.Datetime(
        string='Last Location Update'
    )
    
    # Status and Configuration
    active = fields.Boolean(
        string='Active',
        default=True
    )
    state = fields.Selection([
        ('available', 'Available'),
        ('on_route', 'On Route'),
        ('maintenance', 'Maintenance'),
        ('inactive', 'Inactive')
    ], string='Status', default='available', tracking=True)
    
    # Capacity and Specifications
    max_weight_capacity = fields.Float(
        string='Max Weight Capacity (kg)',
        help='Maximum weight capacity in kilograms'
    )
    max_volume_capacity = fields.Float(
        string='Max Volume Capacity (m³)',
        help='Maximum volume capacity in cubic meters'
    )
    fuel_type = fields.Selection([
        ('gasoline', 'Gasoline'),
        ('diesel', 'Diesel'),
        ('electric', 'Electric'),
        ('hybrid', 'Hybrid')
    ], string='Fuel Type')
    
    # Territory Assignment
    territory_ids = fields.Many2many(
        'van.territory',
        'van_vehicle_territory_rel',
        'vehicle_id',
        'territory_id',
        string='Assigned Territories'
    )
    
    # Routes and Visits
    route_ids = fields.One2many(
        'van.route',
        'vehicle_id',
        string='Routes'
    )
    visit_ids = fields.One2many(
        'van.visit',
        'vehicle_id',
        string='Visits'
    )
    
    # Statistics
    total_routes = fields.Integer(
        string='Total Routes',
        compute='_compute_statistics'
    )
    total_visits = fields.Integer(
        string='Total Visits',
        compute='_compute_statistics'
    )
    total_distance = fields.Float(
        string='Total Distance (km)',
        compute='_compute_statistics'
    )
    
    @api.depends('route_ids', 'visit_ids')
    def _compute_statistics(self):
        for vehicle in self:
            vehicle.total_routes = len(vehicle.route_ids)
            vehicle.total_visits = len(vehicle.visit_ids)
            vehicle.total_distance = sum(vehicle.route_ids.mapped('total_distance'))
    
    @api.constrains('current_latitude', 'current_longitude')
    def _check_coordinates(self):
        for vehicle in self:
            if vehicle.current_latitude and not (-90 <= vehicle.current_latitude <= 90):
                raise ValidationError(_('Latitude must be between -90 and 90 degrees.'))
            if vehicle.current_longitude and not (-180 <= vehicle.current_longitude <= 180):
                raise ValidationError(_('Longitude must be between -180 and 180 degrees.'))
    
    def update_location(self, latitude, longitude):
        """Update vehicle's current location"""
        self.ensure_one()
        self.write({
            'current_latitude': latitude,
            'current_longitude': longitude,
            'last_location_update': fields.Datetime.now()
        })
        
        # Log location update
        self.message_post(
            body=_('Location updated: Lat %s, Lng %s') % (latitude, longitude),
            message_type='notification'
        )
    
    def action_set_on_route(self):
        """Set vehicle status to on route"""
        self.state = 'on_route'
    
    def action_set_available(self):
        """Set vehicle status to available"""
        self.state = 'available'
    
    def action_set_maintenance(self):
        """Set vehicle status to maintenance"""
        self.state = 'maintenance'
