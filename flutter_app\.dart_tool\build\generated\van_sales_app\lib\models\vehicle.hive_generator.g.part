// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VehicleAdapter extends TypeAdapter<Vehicle> {
  @override
  final int typeId = 2;

  @override
  Vehicle read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Vehicle(
      id: fields[0] as int,
      name: fields[1] as String,
      licensePlate: fields[2] as String,
      driverId: fields[3] as int?,
      driverName: fields[4] as String?,
      state: fields[5] as VehicleState,
      currentLatitude: fields[6] as double?,
      currentLongitude: fields[7] as double?,
      lastLocationUpdate: fields[8] as DateTime?,
      maxWeightCapacity: fields[9] as double?,
      maxVolumeCapacity: fields[10] as double?,
      fuelType: fields[11] as String?,
      isActive: fields[12] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Vehicle obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.licensePlate)
      ..writeByte(3)
      ..write(obj.driverId)
      ..writeByte(4)
      ..write(obj.driverName)
      ..writeByte(5)
      ..write(obj.state)
      ..writeByte(6)
      ..write(obj.currentLatitude)
      ..writeByte(7)
      ..write(obj.currentLongitude)
      ..writeByte(8)
      ..write(obj.lastLocationUpdate)
      ..writeByte(9)
      ..write(obj.maxWeightCapacity)
      ..writeByte(10)
      ..write(obj.maxVolumeCapacity)
      ..writeByte(11)
      ..write(obj.fuelType)
      ..writeByte(12)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VehicleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VehicleStateAdapter extends TypeAdapter<VehicleState> {
  @override
  final int typeId = 3;

  @override
  VehicleState read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VehicleState.available;
      case 1:
        return VehicleState.onRoute;
      case 2:
        return VehicleState.maintenance;
      case 3:
        return VehicleState.inactive;
      default:
        return VehicleState.available;
    }
  }

  @override
  void write(BinaryWriter writer, VehicleState obj) {
    switch (obj) {
      case VehicleState.available:
        writer.writeByte(0);
        break;
      case VehicleState.onRoute:
        writer.writeByte(1);
        break;
      case VehicleState.maintenance:
        writer.writeByte(2);
        break;
      case VehicleState.inactive:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VehicleStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
