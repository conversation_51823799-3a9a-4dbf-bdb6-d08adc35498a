import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../config/app_config.dart';

class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();
  NotificationService._();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  static Future<void> initialize() async {
    await instance._initialize();
  }

  Future<void> _initialize() async {
    try {
      if (_isInitialized) return;

      // Android initialization
      const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization
      const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request permissions
      await _requestPermissions();

      _isInitialized = true;
      debugPrint('Notification service initialized');
    } catch (e) {
      debugPrint('Error initializing notification service: $e');
    }
  }

  Future<void> _requestPermissions() async {
    try {
      // Request Android permissions
      await _notifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      // Request iOS permissions
      await _notifications
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // Handle notification tap
    // You can navigate to specific screens based on the payload
  }

  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.defaultPriority,
  }) async {
    try {
      if (!AppConfig.enableNotifications || !_isInitialized) return;

      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'van_sales_channel',
        'Van Sales Notifications',
        channelDescription: 'Notifications for van sales operations',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint('Notification shown: $title');
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    try {
      if (!AppConfig.enableNotifications || !_isInitialized) return;

      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'van_sales_scheduled_channel',
        'Van Sales Scheduled Notifications',
        channelDescription: 'Scheduled notifications for van sales operations',
        importance: Importance.high,
        priority: Priority.high,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.zonedSchedule(
        id,
        title,
        body,
        scheduledTime,
        notificationDetails,
        payload: payload,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );

      debugPrint('Notification scheduled: $title for $scheduledTime');
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
    }
  }

  Future<void> showRouteStartNotification(String routeName) async {
    await showNotification(
      id: 1001,
      title: 'Route Started',
      body: 'Route "$routeName" has been started',
      payload: 'route_started',
    );
  }

  Future<void> showVisitReminderNotification(String customerName, DateTime visitTime) async {
    await showNotification(
      id: 1002,
      title: 'Visit Reminder',
      body: 'Upcoming visit to $customerName at ${_formatTime(visitTime)}',
      payload: 'visit_reminder',
    );
  }

  Future<void> showLocationUpdateNotification() async {
    await showNotification(
      id: 1003,
      title: 'Location Updated',
      body: 'Your location has been updated successfully',
      payload: 'location_updated',
    );
  }

  Future<void> showSyncCompleteNotification() async {
    await showNotification(
      id: 1004,
      title: 'Sync Complete',
      body: 'Data synchronization completed successfully',
      payload: 'sync_complete',
    );
  }

  Future<void> showOrderCreatedNotification(String orderName, double amount) async {
    await showNotification(
      id: 1005,
      title: 'Order Created',
      body: 'Order $orderName created for \$${amount.toStringAsFixed(2)}',
      payload: 'order_created',
    );
  }

  Future<void> showTerritoryViolationNotification() async {
    await showNotification(
      id: 1006,
      title: 'Territory Violation',
      body: 'You are outside your assigned territory',
      payload: 'territory_violation',
      priority: NotificationPriority.high,
    );
  }

  Future<void> scheduleVisitReminder(int visitId, String customerName, DateTime visitTime) async {
    // Schedule reminder 30 minutes before visit
    final reminderTime = visitTime.subtract(const Duration(minutes: 30));
    
    if (reminderTime.isAfter(DateTime.now())) {
      await scheduleNotification(
        id: 2000 + visitId,
        title: 'Visit Reminder',
        body: 'Visit to $customerName in 30 minutes',
        scheduledTime: reminderTime,
        payload: 'visit_reminder_$visitId',
      );
    }
  }

  Future<void> cancelNotification(int id) async {
    try {
      await _notifications.cancel(id);
      debugPrint('Notification cancelled: $id');
    } catch (e) {
      debugPrint('Error cancelling notification: $e');
    }
  }

  Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
      debugPrint('All notifications cancelled');
    } catch (e) {
      debugPrint('Error cancelling all notifications: $e');
    }
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      return await _notifications.pendingNotificationRequests();
    } catch (e) {
      debugPrint('Error getting pending notifications: $e');
      return [];
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  Future<bool> areNotificationsEnabled() async {
    try {
      final androidImplementation = _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      if (androidImplementation != null) {
        return await androidImplementation.areNotificationsEnabled() ?? false;
      }
      return true; // Assume enabled for iOS
    } catch (e) {
      debugPrint('Error checking notification status: $e');
      return false;
    }
  }

  void dispose() {
    // Clean up resources if needed
  }
}

enum NotificationPriority {
  min,
  low,
  defaultPriority,
  high,
  max,
}
