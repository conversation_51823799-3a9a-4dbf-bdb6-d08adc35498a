import json
import logging
from datetime import datetime, timedelta

from odoo import http, fields, _
from odoo.http import request
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class VanSalesAPI(http.Controller):

    def _authenticate_user(self):
        """Authenticate user and return user record"""
        if not request.session.uid:
            return None
        return request.env.user

    def _json_response(self, data=None, error=None, status=200):
        """Return standardized JSON response"""
        response_data = {
            'success': error is None,
            'data': data,
            'error': error,
            'timestamp': fields.Datetime.now().isoformat()
        }
        return request.make_response(
            json.dumps(response_data, default=str),
            headers=[('Content-Type', 'application/json')],
            status=status
        )

    @http.route('/api/van/vehicles', type='http', auth='user', methods=['GET'], csrf=False)
    def get_vehicles(self, **kwargs):
        """Get list of vehicles"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            vehicles = request.env['van.vehicle'].search([('active', '=', True)])
            vehicle_data = []

            for vehicle in vehicles:
                vehicle_data.append({
                    'id': vehicle.id,
                    'name': vehicle.name,
                    'license_plate': vehicle.license_plate,
                    'driver_id': vehicle.driver_id.id if vehicle.driver_id else None,
                    'driver_name': vehicle.driver_id.name if vehicle.driver_id else None,
                    'state': vehicle.state,
                    'current_latitude': vehicle.current_latitude,
                    'current_longitude': vehicle.current_longitude,
                    'last_location_update': vehicle.last_location_update.isoformat() if vehicle.last_location_update else None
                })

            return self._json_response(data=vehicle_data)

        except Exception as e:
            _logger.error('Error getting vehicles: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/vehicle/<int:vehicle_id>/location', type='http', auth='user', methods=['POST'], csrf=False)
    def update_vehicle_location(self, vehicle_id, **kwargs):
        """Update vehicle location"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            data = json.loads(request.httprequest.data.decode('utf-8'))
            latitude = data.get('latitude')
            longitude = data.get('longitude')

            if not latitude or not longitude:
                return self._json_response(error='Latitude and longitude required', status=400)

            vehicle = request.env['van.vehicle'].browse(vehicle_id)
            if not vehicle.exists():
                return self._json_response(error='Vehicle not found', status=404)

            vehicle.update_location(latitude, longitude)

            return self._json_response(data={'message': 'Location updated successfully'})

        except Exception as e:
            _logger.error('Error updating vehicle location: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/routes', type='http', auth='user', methods=['GET'], csrf=False)
    def get_routes(self, **kwargs):
        """Get list of routes"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            # Filter parameters
            date_from = kwargs.get('date_from')
            date_to = kwargs.get('date_to')
            vehicle_id = kwargs.get('vehicle_id')
            state = kwargs.get('state')

            domain = []
            if date_from:
                domain.append(('planned_date', '>=', date_from))
            if date_to:
                domain.append(('planned_date', '<=', date_to))
            if vehicle_id:
                domain.append(('vehicle_id', '=', int(vehicle_id)))
            if state:
                domain.append(('state', '=', state))

            routes = request.env['van.route'].search(domain, order='planned_date desc')
            route_data = []

            for route in routes:
                route_data.append({
                    'id': route.id,
                    'name': route.name,
                    'code': route.code,
                    'planned_date': route.planned_date.isoformat() if route.planned_date else None,
                    'state': route.state,
                    'vehicle_id': route.vehicle_id.id,
                    'vehicle_name': route.vehicle_id.name,
                    'driver_name': route.driver_id.name if route.driver_id else None,
                    'total_visits': route.total_visits,
                    'completed_visits': route.completed_visits,
                    'total_distance': route.total_distance,
                    'estimated_duration': route.estimated_duration
                })

            return self._json_response(data=route_data)

        except Exception as e:
            _logger.error('Error getting routes: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/route/<int:route_id>', type='http', auth='user', methods=['GET'], csrf=False)
    def get_route_details(self, route_id, **kwargs):
        """Get detailed route information"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            route = request.env['van.route'].browse(route_id)
            if not route.exists():
                return self._json_response(error='Route not found', status=404)

            # Get visits with customer information
            visits_data = []
            for visit in route.visit_ids.sorted('sequence'):
                visits_data.append({
                    'id': visit.id,
                    'name': visit.name,
                    'sequence': visit.sequence,
                    'customer_id': visit.customer_id.id,
                    'customer_name': visit.customer_id.name,
                    'customer_latitude': visit.customer_latitude,
                    'customer_longitude': visit.customer_longitude,
                    'visit_date': visit.visit_date.isoformat() if visit.visit_date else None,
                    'planned_time': visit.planned_time.isoformat() if visit.planned_time else None,
                    'state': visit.state,
                    'visit_type': visit.visit_type,
                    'purpose': visit.purpose,
                    'notes': visit.notes
                })

            route_data = {
                'id': route.id,
                'name': route.name,
                'code': route.code,
                'planned_date': route.planned_date.isoformat() if route.planned_date else None,
                'state': route.state,
                'vehicle_id': route.vehicle_id.id,
                'vehicle_name': route.vehicle_id.name,
                'driver_name': route.driver_id.name if route.driver_id else None,
                'start_location': route.start_location,
                'start_latitude': route.start_latitude,
                'start_longitude': route.start_longitude,
                'end_location': route.end_location,
                'end_latitude': route.end_latitude,
                'end_longitude': route.end_longitude,
                'total_distance': route.total_distance,
                'estimated_duration': route.estimated_duration,
                'optimized_route': json.loads(route.optimized_route) if route.optimized_route else None,
                'visits': visits_data,
                'notes': route.notes,
                'special_instructions': route.special_instructions
            }

            return self._json_response(data=route_data)

        except Exception as e:
            _logger.error('Error getting route details: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/route/<int:route_id>/optimize', type='http', auth='user', methods=['POST'], csrf=False)
    def optimize_route(self, route_id, **kwargs):
        """Optimize route"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            route = request.env['van.route'].browse(route_id)
            if not route.exists():
                return self._json_response(error='Route not found', status=404)

            route.action_plan_route()

            return self._json_response(data={'message': 'Route optimized successfully'})

        except Exception as e:
            _logger.error('Error optimizing route: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/customers', type='http', auth='user', methods=['GET'], csrf=False)
    def get_customers(self, **kwargs):
        """Get list of customers"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            # Filter parameters
            territory_id = kwargs.get('territory_id')
            search = kwargs.get('search')
            limit = int(kwargs.get('limit', 100))

            domain = [('active', '=', True)]
            if territory_id:
                domain.append(('territory_id', '=', int(territory_id)))
            if search:
                domain.append(('name', 'ilike', search))

            customers = request.env['van.customer'].search(domain, limit=limit)
            customer_data = []

            for customer in customers:
                customer_data.append({
                    'id': customer.id,
                    'name': customer.name,
                    'latitude': customer.latitude,
                    'longitude': customer.longitude,
                    'address': customer.address,
                    'territory_id': customer.territory_id.id if customer.territory_id else None,
                    'territory_name': customer.territory_id.name if customer.territory_id else None,
                    'customer_type': customer.customer_type,
                    'priority': customer.priority,
                    'preferred_visit_time': customer.preferred_visit_time,
                    'visit_frequency': customer.visit_frequency,
                    'last_visit_date': customer.last_visit_date.isoformat() if customer.last_visit_date else None,
                    'total_visits': customer.total_visits,
                    'total_sales': customer.total_sales
                })

            return self._json_response(data=customer_data)

        except Exception as e:
            _logger.error('Error getting customers: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/territories', type='http', auth='user', methods=['GET'], csrf=False)
    def get_territories(self, **kwargs):
        """Get list of territories"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            territories = request.env['van.territory'].search([('active', '=', True)])
            territory_data = []

            for territory in territories:
                territory_data.append({
                    'id': territory.id,
                    'name': territory.name,
                    'code': territory.code,
                    'territory_type': territory.territory_type,
                    'center_latitude': territory.center_latitude,
                    'center_longitude': territory.center_longitude,
                    'radius': territory.radius,
                    'boundary_coordinates': json.loads(territory.boundary_coordinates) if territory.boundary_coordinates else None,
                    'color': territory.color,
                    'customer_count': territory.customer_count,
                    'manager_name': territory.manager_id.name if territory.manager_id else None
                })

            return self._json_response(data=territory_data)

        except Exception as e:
            _logger.error('Error getting territories: %s', str(e))
            return self._json_response(error=str(e), status=500)

    @http.route('/api/van/territory/<int:territory_id>/validate', type='http', auth='user', methods=['POST'], csrf=False)
    def validate_territory_location(self, territory_id, **kwargs):
        """Validate if location is within territory"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)

            data = json.loads(request.httprequest.data.decode('utf-8'))
            latitude = data.get('latitude')
            longitude = data.get('longitude')

            if not latitude or not longitude:
                return self._json_response(error='Latitude and longitude required', status=400)

            territory = request.env['van.territory'].browse(territory_id)
            if not territory.exists():
                return self._json_response(error='Territory not found', status=404)

            is_valid = territory.is_point_in_territory(latitude, longitude)

            return self._json_response(data={
                'is_valid': is_valid,
                'territory_name': territory.name,
                'message': 'Location is within territory' if is_valid else 'Location is outside territory'
            })

        except Exception as e:
            _logger.error('Error validating territory location: %s', str(e))
            return self._json_response(error=str(e), status=500)
