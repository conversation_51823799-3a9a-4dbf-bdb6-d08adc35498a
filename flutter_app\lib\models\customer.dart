import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer.g.dart';

@HiveType(typeId: 6)
@JsonSerializable()
class Customer {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final double latitude;

  @HiveField(3)
  final double longitude;

  @HiveField(4)
  final String? address;

  @HiveField(5)
  final int? territoryId;

  @HiveField(6)
  final String? territoryName;

  @HiveField(7)
  final CustomerType customerType;

  @HiveField(8)
  final CustomerPriority priority;

  @HiveField(9)
  final PreferredVisitTime preferredVisitTime;

  @HiveField(10)
  final VisitFrequency visitFrequency;

  @HiveField(11)
  final DateTime? lastVisitDate;

  @HiveField(12)
  final int totalVisits;

  @HiveField(13)
  final double totalSales;

  @HiveField(14)
  final String? contactPerson;

  @HiveField(15)
  final String? contactPhone;

  @HiveField(16)
  final String? deliveryInstructions;

  @HiveField(17)
  final String? accessRequirements;

  @HiveField(18)
  final bool isActive;

  const Customer({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    this.address,
    this.territoryId,
    this.territoryName,
    this.customerType = CustomerType.retail,
    this.priority = CustomerPriority.medium,
    this.preferredVisitTime = PreferredVisitTime.anytime,
    this.visitFrequency = VisitFrequency.weekly,
    this.lastVisitDate,
    this.totalVisits = 0,
    this.totalSales = 0.0,
    this.contactPerson,
    this.contactPhone,
    this.deliveryInstructions,
    this.accessRequirements,
    this.isActive = true,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => _$CustomerFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  Customer copyWith({
    int? id,
    String? name,
    double? latitude,
    double? longitude,
    String? address,
    int? territoryId,
    String? territoryName,
    CustomerType? customerType,
    CustomerPriority? priority,
    PreferredVisitTime? preferredVisitTime,
    VisitFrequency? visitFrequency,
    DateTime? lastVisitDate,
    int? totalVisits,
    double? totalSales,
    String? contactPerson,
    String? contactPhone,
    String? deliveryInstructions,
    String? accessRequirements,
    bool? isActive,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      territoryId: territoryId ?? this.territoryId,
      territoryName: territoryName ?? this.territoryName,
      customerType: customerType ?? this.customerType,
      priority: priority ?? this.priority,
      preferredVisitTime: preferredVisitTime ?? this.preferredVisitTime,
      visitFrequency: visitFrequency ?? this.visitFrequency,
      lastVisitDate: lastVisitDate ?? this.lastVisitDate,
      totalVisits: totalVisits ?? this.totalVisits,
      totalSales: totalSales ?? this.totalSales,
      contactPerson: contactPerson ?? this.contactPerson,
      contactPhone: contactPhone ?? this.contactPhone,
      deliveryInstructions: deliveryInstructions ?? this.deliveryInstructions,
      accessRequirements: accessRequirements ?? this.accessRequirements,
      isActive: isActive ?? this.isActive,
    );
  }

  String get customerTypeDisplayName {
    switch (customerType) {
      case CustomerType.retail:
        return 'Retail';
      case CustomerType.wholesale:
        return 'Wholesale';
      case CustomerType.distributor:
        return 'Distributor';
      case CustomerType.restaurant:
        return 'Restaurant';
      case CustomerType.supermarket:
        return 'Supermarket';
    }
  }

  String get priorityDisplayName {
    switch (priority) {
      case CustomerPriority.low:
        return 'Low';
      case CustomerPriority.medium:
        return 'Medium';
      case CustomerPriority.high:
        return 'High';
      case CustomerPriority.critical:
        return 'Critical';
    }
  }

  String get preferredVisitTimeDisplayName {
    switch (preferredVisitTime) {
      case PreferredVisitTime.morning:
        return 'Morning (8:00-12:00)';
      case PreferredVisitTime.afternoon:
        return 'Afternoon (12:00-17:00)';
      case PreferredVisitTime.evening:
        return 'Evening (17:00-20:00)';
      case PreferredVisitTime.anytime:
        return 'Anytime';
    }
  }

  String get visitFrequencyDisplayName {
    switch (visitFrequency) {
      case VisitFrequency.daily:
        return 'Daily';
      case VisitFrequency.weekly:
        return 'Weekly';
      case VisitFrequency.biweekly:
        return 'Bi-weekly';
      case VisitFrequency.monthly:
        return 'Monthly';
      case VisitFrequency.onDemand:
        return 'On Demand';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Customer && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Customer{id: $id, name: $name, customerType: $customerType, priority: $priority}';
  }
}

@HiveType(typeId: 7)
enum CustomerType {
  @HiveField(0)
  retail,
  @HiveField(1)
  wholesale,
  @HiveField(2)
  distributor,
  @HiveField(3)
  restaurant,
  @HiveField(4)
  supermarket,
}

@HiveType(typeId: 8)
enum CustomerPriority {
  @HiveField(0)
  low,
  @HiveField(1)
  medium,
  @HiveField(2)
  high,
  @HiveField(3)
  critical,
}

@HiveType(typeId: 9)
enum PreferredVisitTime {
  @HiveField(0)
  morning,
  @HiveField(1)
  afternoon,
  @HiveField(2)
  evening,
  @HiveField(3)
  anytime,
}

@HiveType(typeId: 10)
enum VisitFrequency {
  @HiveField(0)
  daily,
  @HiveField(1)
  weekly,
  @HiveField(2)
  biweekly,
  @HiveField(3)
  monthly,
  @HiveField(4)
  onDemand,
}
