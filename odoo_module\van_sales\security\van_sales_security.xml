<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Van Sales Categories -->
        <record id="module_category_van_sales" model="ir.module.category">
            <field name="name">Van Sales</field>
            <field name="description">Van Sales and Distribution Management</field>
            <field name="sequence">20</field>
        </record>
        
        <!-- Van Sales User Group -->
        <record id="group_van_sales_user" model="res.groups">
            <field name="name">Van Sales User</field>
            <field name="category_id" ref="module_category_van_sales"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Van sales users can view and manage their assigned routes, visits, and orders.</field>
        </record>
        
        <!-- Van Sales Manager Group -->
        <record id="group_van_sales_manager" model="res.groups">
            <field name="name">Van Sales Manager</field>
            <field name="category_id" ref="module_category_van_sales"/>
            <field name="implied_ids" eval="[(4, ref('group_van_sales_user'))]"/>
            <field name="comment">Van sales managers can manage all van sales operations, routes, territories, and reports.</field>
        </record>
        
        <!-- Van Sales Administrator Group -->
        <record id="group_van_sales_admin" model="res.groups">
            <field name="name">Van Sales Administrator</field>
            <field name="category_id" ref="module_category_van_sales"/>
            <field name="implied_ids" eval="[(4, ref('group_van_sales_manager'))]"/>
            <field name="comment">Van sales administrators have full access to all van sales features and configurations.</field>
        </record>
        
        <!-- Record Rules -->
        
        <!-- Van Vehicle Rules -->
        <record id="van_vehicle_user_rule" model="ir.rule">
            <field name="name">Van Vehicle: User Access</field>
            <field name="model_id" ref="model_van_vehicle"/>
            <field name="domain_force">[('driver_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="van_vehicle_manager_rule" model="ir.rule">
            <field name="name">Van Vehicle: Manager Access</field>
            <field name="model_id" ref="model_van_vehicle"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Van Route Rules -->
        <record id="van_route_user_rule" model="ir.rule">
            <field name="name">Van Route: User Access</field>
            <field name="model_id" ref="model_van_route"/>
            <field name="domain_force">[('driver_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="van_route_manager_rule" model="ir.rule">
            <field name="name">Van Route: Manager Access</field>
            <field name="model_id" ref="model_van_route"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Van Visit Rules -->
        <record id="van_visit_user_rule" model="ir.rule">
            <field name="name">Van Visit: User Access</field>
            <field name="model_id" ref="model_van_visit"/>
            <field name="domain_force">[('vehicle_id.driver_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="van_visit_manager_rule" model="ir.rule">
            <field name="name">Van Visit: Manager Access</field>
            <field name="model_id" ref="model_van_visit"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Van Customer Rules -->
        <record id="van_customer_user_rule" model="ir.rule">
            <field name="name">Van Customer: User Access</field>
            <field name="model_id" ref="model_van_customer"/>
            <field name="domain_force">[('territory_id.vehicle_ids.driver_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="van_customer_manager_rule" model="ir.rule">
            <field name="name">Van Customer: Manager Access</field>
            <field name="model_id" ref="model_van_customer"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Van Territory Rules -->
        <record id="van_territory_user_rule" model="ir.rule">
            <field name="name">Van Territory: User Access</field>
            <field name="model_id" ref="model_van_territory"/>
            <field name="domain_force">[('vehicle_ids.driver_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="van_territory_manager_rule" model="ir.rule">
            <field name="name">Van Territory: Manager Access</field>
            <field name="model_id" ref="model_van_territory"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_van_sales_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
    </data>
</odoo>
