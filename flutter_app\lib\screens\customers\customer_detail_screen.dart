import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class CustomerDetailScreen extends ConsumerWidget {
  final int customerId;
  
  const CustomerDetailScreen({super.key, required this.customerId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: Text('Customer $customerId')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.person, size: 64, color: AppTheme.customerColor),
            const SizedBox(height: 16),
            Text('Customer Detail $customerId', style: AppTheme.headingMedium),
            const SizedBox(height: 8),
            const Text('Customer details coming soon', style: AppTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
