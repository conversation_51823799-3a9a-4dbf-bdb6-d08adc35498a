import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/config/app_theme.dart';
import '../../core/providers/auth_provider.dart';

class QuickActions extends ConsumerWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissions = ref.watch(userPermissionsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.0,
              children: [
                _QuickActionItem(
                  icon: Icons.route,
                  label: 'Routes',
                  color: AppTheme.routeColor,
                  onTap: () => context.go('/routes'),
                ),
                _QuickActionItem(
                  icon: Icons.people,
                  label: 'Customers',
                  color: AppTheme.customerColor,
                  onTap: () => context.go('/customers'),
                ),
                _QuickActionItem(
                  icon: Icons.assignment,
                  label: 'Visits',
                  color: AppTheme.visitColor,
                  onTap: () => context.go('/visits'),
                ),
                _QuickActionItem(
                  icon: Icons.map,
                  label: 'Map',
                  color: AppTheme.territoryColor,
                  onTap: () => context.go('/map'),
                ),
                if (permissions?.canCreateOrders == true)
                  _QuickActionItem(
                    icon: Icons.add_shopping_cart,
                    label: 'New Order',
                    color: AppTheme.successColor,
                    onTap: () {
                      // TODO: Navigate to new order screen
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('New Order feature coming soon'),
                        ),
                      );
                    },
                  ),
                _QuickActionItem(
                  icon: Icons.sync,
                  label: 'Sync',
                  color: AppTheme.infoColor,
                  onTap: () {
                    // TODO: Trigger sync
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Sync started'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _QuickActionItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _QuickActionItem({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.2),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
