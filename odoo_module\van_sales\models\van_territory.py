from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json
import logging

_logger = logging.getLogger(__name__)


class VanTerritory(models.Model):
    _name = 'van.territory'
    _description = 'Van Sales Territory'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Territory Name',
        required=True,
        tracking=True
    )
    code = fields.Char(
        string='Territory Code',
        required=True,
        tracking=True
    )
    description = fields.Text(
        string='Description'
    )
    
    # Geographic Boundaries
    boundary_coordinates = fields.Text(
        string='Boundary Coordinates',
        help='JSON array of coordinates defining the territory boundary'
    )
    center_latitude = fields.Float(
        string='Center Latitude',
        digits=(10, 6)
    )
    center_longitude = fields.Float(
        string='Center Longitude',
        digits=(10, 6)
    )
    radius = fields.Float(
        string='Radius (km)',
        help='Territory radius in kilometers for circular territories'
    )
    
    # Territory Type
    territory_type = fields.Selection([
        ('polygon', 'Polygon'),
        ('circle', 'Circle'),
        ('custom', 'Custom Shape')
    ], string='Territory Type', default='circle', required=True)
    
    # Status and Configuration
    active = fields.Boolean(
        string='Active',
        default=True
    )
    color = fields.Char(
        string='Map Color',
        default='#FF0000',
        help='Color to display territory on map'
    )
    
    # Assignments
    vehicle_ids = fields.Many2many(
        'van.vehicle',
        'van_vehicle_territory_rel',
        'territory_id',
        'vehicle_id',
        string='Assigned Vehicles'
    )
    customer_ids = fields.One2many(
        'res.partner',
        'van_territory_id',
        string='Customers in Territory'
    )
    
    # Manager and Team
    manager_id = fields.Many2one(
        'res.users',
        string='Territory Manager',
        tracking=True
    )
    sales_team_id = fields.Many2one(
        'crm.team',
        string='Sales Team'
    )
    
    # Statistics
    customer_count = fields.Integer(
        string='Customer Count',
        compute='_compute_statistics'
    )
    total_sales = fields.Monetary(
        string='Total Sales',
        compute='_compute_statistics',
        currency_field='currency_id'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    @api.depends('customer_ids')
    def _compute_statistics(self):
        for territory in self:
            territory.customer_count = len(territory.customer_ids)
            # Calculate total sales from customers in this territory
            sales_orders = self.env['sale.order'].search([
                ('partner_id', 'in', territory.customer_ids.ids),
                ('state', 'in', ['sale', 'done'])
            ])
            territory.total_sales = sum(sales_orders.mapped('amount_total'))
    
    @api.constrains('center_latitude', 'center_longitude')
    def _check_coordinates(self):
        for territory in self:
            if territory.center_latitude and not (-90 <= territory.center_latitude <= 90):
                raise ValidationError(_('Latitude must be between -90 and 90 degrees.'))
            if territory.center_longitude and not (-180 <= territory.center_longitude <= 180):
                raise ValidationError(_('Longitude must be between -180 and 180 degrees.'))
    
    @api.constrains('boundary_coordinates')
    def _check_boundary_coordinates(self):
        for territory in self:
            if territory.boundary_coordinates:
                try:
                    coordinates = json.loads(territory.boundary_coordinates)
                    if not isinstance(coordinates, list):
                        raise ValidationError(_('Boundary coordinates must be a JSON array.'))
                    for coord in coordinates:
                        if not isinstance(coord, list) or len(coord) != 2:
                            raise ValidationError(_('Each coordinate must be an array of [longitude, latitude].'))
                        lng, lat = coord
                        if not (-180 <= lng <= 180) or not (-90 <= lat <= 90):
                            raise ValidationError(_('Invalid coordinate values.'))
                except json.JSONDecodeError:
                    raise ValidationError(_('Invalid JSON format for boundary coordinates.'))
    
    def is_point_in_territory(self, latitude, longitude):
        """Check if a point is within the territory boundaries"""
        self.ensure_one()
        
        if self.territory_type == 'circle':
            return self._is_point_in_circle(latitude, longitude)
        elif self.territory_type == 'polygon':
            return self._is_point_in_polygon(latitude, longitude)
        else:
            # Custom logic can be implemented here
            return True
    
    def _is_point_in_circle(self, latitude, longitude):
        """Check if point is within circular territory"""
        if not (self.center_latitude and self.center_longitude and self.radius):
            return False
        
        from geopy.distance import geodesic
        center = (self.center_latitude, self.center_longitude)
        point = (latitude, longitude)
        distance = geodesic(center, point).kilometers
        
        return distance <= self.radius
    
    def _is_point_in_polygon(self, latitude, longitude):
        """Check if point is within polygon territory using ray casting algorithm"""
        if not self.boundary_coordinates:
            return False
        
        try:
            coordinates = json.loads(self.boundary_coordinates)
            return self._point_in_polygon(longitude, latitude, coordinates)
        except (json.JSONDecodeError, Exception):
            return False
    
    def _point_in_polygon(self, x, y, polygon):
        """Ray casting algorithm to determine if point is inside polygon"""
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def get_territory_bounds(self):
        """Get territory bounds for map display"""
        self.ensure_one()
        
        if self.territory_type == 'circle' and self.center_latitude and self.center_longitude:
            # Calculate approximate bounds for circle
            lat_offset = self.radius / 111.0  # Approximate km per degree latitude
            lng_offset = self.radius / (111.0 * abs(self.center_latitude))
            
            return {
                'north': self.center_latitude + lat_offset,
                'south': self.center_latitude - lat_offset,
                'east': self.center_longitude + lng_offset,
                'west': self.center_longitude - lng_offset
            }
        elif self.territory_type == 'polygon' and self.boundary_coordinates:
            try:
                coordinates = json.loads(self.boundary_coordinates)
                lats = [coord[1] for coord in coordinates]
                lngs = [coord[0] for coord in coordinates]
                
                return {
                    'north': max(lats),
                    'south': min(lats),
                    'east': max(lngs),
                    'west': min(lngs)
                }
            except (json.JSONDecodeError, Exception):
                pass
        
        return None
