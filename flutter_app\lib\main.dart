import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/app.dart';
import 'core/config/app_config.dart';
import 'core/services/storage_service.dart';
import 'core/services/location_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/background_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Hive
  await Hive.initFlutter();
  
  // Initialize services
  await _initializeServices();
  
  runApp(
    const ProviderScope(
      child: VanSalesApp(),
    ),
  );
}

Future<void> _initializeServices() async {
  try {
    // Initialize storage
    await StorageService.initialize();
    
    // Initialize location service
    await LocationService.initialize();
    
    // Initialize notifications
    await NotificationService.initialize();
    
    // Initialize background service
    await BackgroundService.initialize();
    
    // Load app configuration
    await AppConfig.load();
    
  } catch (e) {
    debugPrint('Error initializing services: $e');
  }
}
