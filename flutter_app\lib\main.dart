import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/app.dart';
import 'core/config/app_config.dart';
import 'core/services/storage_service.dart';
import 'core/services/location_service.dart';
import 'core/services/notification_service.dart';
import 'models/user.dart';
import 'models/vehicle.dart';
import 'models/route.dart';
import 'models/customer.dart';
import 'models/visit.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  _registerHiveAdapters();

  // Initialize services
  await _initializeServices();

  runApp(
    const ProviderScope(
      child: VanSalesApp(),
    ),
  );
}

void _registerHiveAdapters() {
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(VehicleAdapter());
  Hive.registerAdapter(RouteAdapter());
  Hive.registerAdapter(CustomerAdapter());
  Hive.registerAdapter(VisitAdapter());
}

Future<void> _initializeServices() async {
  try {
    // Load app configuration first
    await AppConfig.load();

    // Initialize storage
    await StorageService.initialize();

    // Initialize location service
    await LocationService.initialize();

    // Initialize notifications
    await NotificationService.initialize();

  } catch (e) {
    debugPrint('Error initializing services: $e');
  }
}
