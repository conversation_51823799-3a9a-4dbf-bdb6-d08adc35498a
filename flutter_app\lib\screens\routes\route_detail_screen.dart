import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class RouteDetailScreen extends ConsumerStatefulWidget {
  final int routeId;
  
  const RouteDetailScreen({super.key, required this.routeId});

  @override
  ConsumerState<RouteDetailScreen> createState() => _RouteDetailScreenState();
}

class _RouteDetailScreenState extends ConsumerState<RouteDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Route ${widget.routeId}'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.route,
              size: 64,
              color: AppTheme.routeColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Route Detail ${widget.routeId}',
              style: AppTheme.headingMedium,
            ),
            const SizedBox(height: 8),
            const Text(
              'Route details coming soon',
              style: AppTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
