import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class User {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final int? vehicleId;

  @HiveField(4)
  final String? vehicleName;

  @HiveField(5)
  final String? sessionId;

  @HiveField(6)
  final UserPermissions permissions;

  @HiveField(7)
  final DateTime? lastLogin;

  @HiveField(8)
  final bool isActive;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.vehicleId,
    this.vehicleName,
    this.sessionId,
    required this.permissions,
    this.lastLogin,
    this.isActive = true,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserTo<PERSON>son(this);

  User copyWith({
    int? id,
    String? name,
    String? email,
    int? vehicleId,
    String? vehicleName,
    String? sessionId,
    UserPermissions? permissions,
    DateTime? lastLogin,
    bool? isActive,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      vehicleId: vehicleId ?? this.vehicleId,
      vehicleName: vehicleName ?? this.vehicleName,
      sessionId: sessionId ?? this.sessionId,
      permissions: permissions ?? this.permissions,
      lastLogin: lastLogin ?? this.lastLogin,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User{id: $id, name: $name, email: $email, vehicleId: $vehicleId}';
  }
}

@HiveType(typeId: 1)
@JsonSerializable()
class UserPermissions {
  @HiveField(0)
  final bool canCreateOrders;

  @HiveField(1)
  final bool canManageRoutes;

  @HiveField(2)
  final bool canViewReports;

  @HiveField(3)
  final bool canManageCustomers;

  @HiveField(4)
  final bool canManageVehicles;

  const UserPermissions({
    this.canCreateOrders = false,
    this.canManageRoutes = false,
    this.canViewReports = false,
    this.canManageCustomers = false,
    this.canManageVehicles = false,
  });

  factory UserPermissions.fromJson(Map<String, dynamic> json) =>
      _$UserPermissionsFromJson(json);
  Map<String, dynamic> toJson() => _$UserPermissionsToJson(this);

  UserPermissions copyWith({
    bool? canCreateOrders,
    bool? canManageRoutes,
    bool? canViewReports,
    bool? canManageCustomers,
    bool? canManageVehicles,
  }) {
    return UserPermissions(
      canCreateOrders: canCreateOrders ?? this.canCreateOrders,
      canManageRoutes: canManageRoutes ?? this.canManageRoutes,
      canViewReports: canViewReports ?? this.canViewReports,
      canManageCustomers: canManageCustomers ?? this.canManageCustomers,
      canManageVehicles: canManageVehicles ?? this.canManageVehicles,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPermissions &&
          runtimeType == other.runtimeType &&
          canCreateOrders == other.canCreateOrders &&
          canManageRoutes == other.canManageRoutes &&
          canViewReports == other.canViewReports &&
          canManageCustomers == other.canManageCustomers &&
          canManageVehicles == other.canManageVehicles;

  @override
  int get hashCode =>
      canCreateOrders.hashCode ^
      canManageRoutes.hashCode ^
      canViewReports.hashCode ^
      canManageCustomers.hashCode ^
      canManageVehicles.hashCode;
}
