{
    'name': 'Van Sales and Distribution Management',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Comprehensive van sales management with GPS tracking and route optimization',
    'description': """
Van Sales and Distribution Management System
===========================================

This module provides comprehensive van sales and distribution management capabilities including:

* Customer location management with GPS coordinates
* Sales territory restrictions with geofencing
* Vehicle tracking and route management
* Route planning with interactive maps
* Optimal route calculation algorithms
* Automatic customer visit scheduling
* Real-time GPS tracking integration
* Mobile app synchronization

Key Features:
* GPS-based customer location management
* Territory-based sales restrictions
* Interactive route planning
* Vehicle tracking and monitoring
* Customer visit scheduling
* Sales performance analytics
* Mobile app integration
* Offline capability support
    """,
    'author': 'Van Sales Team',
    'website': 'https://www.vansales.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'sale',
        'stock',
        'fleet',
        'contacts',
        'web',
        'web_map',
    ],
    'data': [
        # Security
        'security/van_sales_security.xml',
        'security/ir.model.access.csv',
        
        # Data
        'data/van_sales_data.xml',
        
        # Views
        'views/van_sales_menus.xml',
        'views/van_vehicle_views.xml',
        'views/van_route_views.xml',
        'views/van_customer_views.xml',
        'views/van_territory_views.xml',
        'views/van_visit_views.xml',
        'views/van_sales_order_views.xml',
        'views/van_dashboard_views.xml',
        
        # Reports
        'reports/van_sales_reports.xml',
        'reports/van_route_report_templates.xml',
        'reports/van_sales_report_templates.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'van_sales/static/src/css/van_sales.css',
            'van_sales/static/src/js/van_map_widget.js',
            'van_sales/static/src/js/van_route_planner.js',
            'van_sales/static/src/js/van_dashboard.js',
        ],
        'web.assets_frontend': [
            'van_sales/static/src/css/van_sales_frontend.css',
        ],
    },
    'demo': [
        'demo/van_sales_demo.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    'external_dependencies': {
        'python': [
            'geopy',
            'folium',
            'requests',
        ],
    },
}
