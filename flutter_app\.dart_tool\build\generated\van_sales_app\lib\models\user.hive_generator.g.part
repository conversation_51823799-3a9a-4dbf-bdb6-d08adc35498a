// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 0;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      id: fields[0] as int,
      name: fields[1] as String,
      email: fields[2] as String,
      vehicleId: fields[3] as int?,
      vehicleName: fields[4] as String?,
      sessionId: fields[5] as String?,
      permissions: fields[6] as UserPermissions,
      lastLogin: fields[7] as DateTime?,
      isActive: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.vehicleId)
      ..writeByte(4)
      ..write(obj.vehicleName)
      ..writeByte(5)
      ..write(obj.sessionId)
      ..writeByte(6)
      ..write(obj.permissions)
      ..writeByte(7)
      ..write(obj.lastLogin)
      ..writeByte(8)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserPermissionsAdapter extends TypeAdapter<UserPermissions> {
  @override
  final int typeId = 1;

  @override
  UserPermissions read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserPermissions(
      canCreateOrders: fields[0] as bool,
      canManageRoutes: fields[1] as bool,
      canViewReports: fields[2] as bool,
      canManageCustomers: fields[3] as bool,
      canManageVehicles: fields[4] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, UserPermissions obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.canCreateOrders)
      ..writeByte(1)
      ..write(obj.canManageRoutes)
      ..writeByte(2)
      ..write(obj.canViewReports)
      ..writeByte(3)
      ..write(obj.canManageCustomers)
      ..writeByte(4)
      ..write(obj.canManageVehicles);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPermissionsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
