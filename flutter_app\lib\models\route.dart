import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'visit.dart';

part 'route.g.dart';

@HiveType(typeId: 4)
@JsonSerializable()
class VanRoute {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String code;

  @HiveField(3)
  final DateTime plannedDate;

  @HiveField(4)
  final RouteState state;

  @HiveField(5)
  final int vehicleId;

  @HiveField(6)
  final String vehicleName;

  @HiveField(7)
  final String? driverName;

  @HiveField(8)
  final String? startLocation;

  @HiveField(9)
  final double? startLatitude;

  @HiveField(10)
  final double? startLongitude;

  @HiveField(11)
  final String? endLocation;

  @HiveField(12)
  final double? endLatitude;

  @HiveField(13)
  final double? endLongitude;

  @HiveField(14)
  final double totalDistance;

  @HiveField(15)
  final double estimatedDuration;

  @HiveField(16)
  final double? actualDistance;

  @HiveField(17)
  final double? actualDuration;

  @HiveField(18)
  final List<Visit> visits;

  @HiveField(19)
  final String? notes;

  @HiveField(20)
  final String? specialInstructions;

  @HiveField(21)
  final DateTime? startTime;

  @HiveField(22)
  final DateTime? endTime;

  @HiveField(23)
  final Map<String, dynamic>? optimizedRoute;

  const VanRoute({
    required this.id,
    required this.name,
    required this.code,
    required this.plannedDate,
    this.state = RouteState.draft,
    required this.vehicleId,
    required this.vehicleName,
    this.driverName,
    this.startLocation,
    this.startLatitude,
    this.startLongitude,
    this.endLocation,
    this.endLatitude,
    this.endLongitude,
    this.totalDistance = 0.0,
    this.estimatedDuration = 0.0,
    this.actualDistance,
    this.actualDuration,
    this.visits = const [],
    this.notes,
    this.specialInstructions,
    this.startTime,
    this.endTime,
    this.optimizedRoute,
  });

  factory VanRoute.fromJson(Map<String, dynamic> json) => _$VanRouteFromJson(json);
  Map<String, dynamic> toJson() => _$VanRouteToJson(this);

  VanRoute copyWith({
    int? id,
    String? name,
    String? code,
    DateTime? plannedDate,
    RouteState? state,
    int? vehicleId,
    String? vehicleName,
    String? driverName,
    String? startLocation,
    double? startLatitude,
    double? startLongitude,
    String? endLocation,
    double? endLatitude,
    double? endLongitude,
    double? totalDistance,
    double? estimatedDuration,
    double? actualDistance,
    double? actualDuration,
    List<Visit>? visits,
    String? notes,
    String? specialInstructions,
    DateTime? startTime,
    DateTime? endTime,
    Map<String, dynamic>? optimizedRoute,
  }) {
    return VanRoute(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      plannedDate: plannedDate ?? this.plannedDate,
      state: state ?? this.state,
      vehicleId: vehicleId ?? this.vehicleId,
      vehicleName: vehicleName ?? this.vehicleName,
      driverName: driverName ?? this.driverName,
      startLocation: startLocation ?? this.startLocation,
      startLatitude: startLatitude ?? this.startLatitude,
      startLongitude: startLongitude ?? this.startLongitude,
      endLocation: endLocation ?? this.endLocation,
      endLatitude: endLatitude ?? this.endLatitude,
      endLongitude: endLongitude ?? this.endLongitude,
      totalDistance: totalDistance ?? this.totalDistance,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      actualDistance: actualDistance ?? this.actualDistance,
      actualDuration: actualDuration ?? this.actualDuration,
      visits: visits ?? this.visits,
      notes: notes ?? this.notes,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      optimizedRoute: optimizedRoute ?? this.optimizedRoute,
    );
  }

  int get totalVisits => visits.length;
  int get completedVisits => visits.where((v) => v.state == VisitState.completed).length;
  int get pendingVisits => totalVisits - completedVisits;
  double get completionRate => totalVisits > 0 ? (completedVisits / totalVisits) * 100 : 0.0;

  bool get canStart => state == RouteState.planned;
  bool get canComplete => state == RouteState.inProgress;
  bool get isActive => state == RouteState.inProgress;
  bool get isCompleted => state == RouteState.completed;

  String get stateDisplayName {
    switch (state) {
      case RouteState.draft:
        return 'Draft';
      case RouteState.planned:
        return 'Planned';
      case RouteState.inProgress:
        return 'In Progress';
      case RouteState.completed:
        return 'Completed';
      case RouteState.cancelled:
        return 'Cancelled';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VanRoute && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VanRoute{id: $id, name: $name, code: $code, state: $state, totalVisits: $totalVisits}';
  }
}

@HiveType(typeId: 5)
enum RouteState {
  @HiveField(0)
  draft,
  @HiveField(1)
  planned,
  @HiveField(2)
  inProgress,
  @HiveField(3)
  completed,
  @HiveField(4)
  cancelled,
}
