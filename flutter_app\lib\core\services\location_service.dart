import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../config/app_config.dart';

class LocationService {
  static LocationService? _instance;
  static LocationService get instance => _instance ??= LocationService._();
  LocationService._();

  StreamSubscription<Position>? _positionStream;
  Position? _currentPosition;
  bool _isTracking = false;
  
  final StreamController<Position> _positionController = StreamController<Position>.broadcast();
  Stream<Position> get positionStream => _positionController.stream;

  Position? get currentPosition => _currentPosition;
  bool get isTracking => _isTracking;

  static Future<void> initialize() async {
    await instance._checkPermissions();
  }

  Future<bool> _checkPermissions() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled.');
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking location permissions: $e');
      return false;
    }
  }

  Future<Position?> getCurrentLocation() async {
    try {
      if (!await _checkPermissions()) {
        return null;
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _currentPosition = position;
      return position;
    } catch (e) {
      debugPrint('Error getting current location: $e');
      return null;
    }
  }

  Future<bool> startTracking() async {
    try {
      if (_isTracking) {
        debugPrint('Location tracking is already active');
        return true;
      }

      if (!await _checkPermissions()) {
        return false;
      }

      if (!AppConfig.enableLocationTracking) {
        debugPrint('Location tracking is disabled in settings');
        return false;
      }

      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      _positionStream = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _currentPosition = position;
          _positionController.add(position);
          debugPrint('Location updated: ${position.latitude}, ${position.longitude}');
        },
        onError: (error) {
          debugPrint('Location tracking error: $error');
        },
      );

      _isTracking = true;
      debugPrint('Location tracking started');
      return true;
    } catch (e) {
      debugPrint('Error starting location tracking: $e');
      return false;
    }
  }

  Future<void> stopTracking() async {
    try {
      await _positionStream?.cancel();
      _positionStream = null;
      _isTracking = false;
      debugPrint('Location tracking stopped');
    } catch (e) {
      debugPrint('Error stopping location tracking: $e');
    }
  }

  Future<double> calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) async {
    try {
      return Geolocator.distanceBetween(
        startLatitude,
        startLongitude,
        endLatitude,
        endLongitude,
      );
    } catch (e) {
      debugPrint('Error calculating distance: $e');
      return 0.0;
    }
  }

  Future<double> calculateDistanceFromCurrent(
    double latitude,
    double longitude,
  ) async {
    try {
      final currentPos = _currentPosition ?? await getCurrentLocation();
      if (currentPos == null) return 0.0;

      return calculateDistance(
        currentPos.latitude,
        currentPos.longitude,
        latitude,
        longitude,
      );
    } catch (e) {
      debugPrint('Error calculating distance from current location: $e');
      return 0.0;
    }
  }

  Future<bool> isLocationWithinRadius(
    double centerLatitude,
    double centerLongitude,
    double radiusInMeters,
    {Position? position}
  ) async {
    try {
      final pos = position ?? _currentPosition ?? await getCurrentLocation();
      if (pos == null) return false;

      final distance = await calculateDistance(
        pos.latitude,
        pos.longitude,
        centerLatitude,
        centerLongitude,
      );

      return distance <= radiusInMeters;
    } catch (e) {
      debugPrint('Error checking location within radius: $e');
      return false;
    }
  }

  Future<LocationAccuracy> getLocationAccuracy() async {
    try {
      final position = await getCurrentLocation();
      if (position == null) return LocationAccuracy.none;

      final accuracy = position.accuracy;
      if (accuracy <= 5) return LocationAccuracy.best;
      if (accuracy <= 10) return LocationAccuracy.high;
      if (accuracy <= 100) return LocationAccuracy.medium;
      if (accuracy <= 1000) return LocationAccuracy.low;
      return LocationAccuracy.lowest;
    } catch (e) {
      debugPrint('Error getting location accuracy: $e');
      return LocationAccuracy.none;
    }
  }

  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      debugPrint('Error opening location settings: $e');
      return false;
    }
  }

  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  String getLocationStatusMessage() {
    if (!_isTracking) return 'Location tracking is disabled';
    if (_currentPosition == null) return 'Getting location...';
    
    final accuracy = _currentPosition!.accuracy;
    if (accuracy <= 5) return 'High accuracy location';
    if (accuracy <= 10) return 'Good accuracy location';
    if (accuracy <= 100) return 'Medium accuracy location';
    return 'Low accuracy location';
  }

  Map<String, dynamic> getLocationInfo() {
    return {
      'isTracking': _isTracking,
      'hasCurrentPosition': _currentPosition != null,
      'latitude': _currentPosition?.latitude,
      'longitude': _currentPosition?.longitude,
      'accuracy': _currentPosition?.accuracy,
      'altitude': _currentPosition?.altitude,
      'heading': _currentPosition?.heading,
      'speed': _currentPosition?.speed,
      'timestamp': _currentPosition?.timestamp?.toIso8601String(),
    };
  }

  void dispose() {
    _positionStream?.cancel();
    _positionController.close();
  }
}
