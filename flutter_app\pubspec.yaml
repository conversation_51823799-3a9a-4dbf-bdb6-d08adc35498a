name: van_sales_app
description: Van Sales and Distribution Management Mobile Application
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.7
  cached_network_image: ^3.3.0

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^12.1.3

  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Location & Maps
  geolocator: ^10.1.0
  permission_handler: ^11.0.1
  google_maps_flutter: ^2.5.0
  flutter_polyline_points: ^2.0.0
  geocoding: ^2.1.1

  # Camera & Media
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  path_provider: ^2.1.1

  # Utilities
  intl: ^0.18.1
  uuid: ^4.1.0
  connectivity_plus: ^5.0.1
  device_info_plus: ^9.1.0
  package_info_plus: ^4.2.0

  # Forms & Validation
  reactive_forms: ^16.1.1

  # Charts & Analytics
  fl_chart: ^0.64.0

  # QR Code
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0

  # Background Tasks
  workmanager: ^0.5.2

  # Notifications
  flutter_local_notifications: ^16.1.0

  # File Handling
  file_picker: ^6.1.1
  open_file: ^3.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^3.0.1

  # Testing
  mockito: ^5.4.2
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/generated
