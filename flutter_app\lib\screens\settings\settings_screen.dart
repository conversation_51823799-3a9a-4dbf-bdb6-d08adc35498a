import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings, size: 64, color: AppTheme.primaryColor),
            SizedBox(height: 16),
            Text('Settings Screen', style: AppTheme.headingMedium),
            SizedBox(height: 8),
            Text('Settings coming soon', style: AppTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
