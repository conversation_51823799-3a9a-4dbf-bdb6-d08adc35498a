# Van Sales and Distribution Management System

A comprehensive solution for managing van sales operations with GPS tracking, route optimization, and territory management.

## 🚀 Project Status

**Current Status**: Core backend models and API structure implemented
**Next Phase**: Complete Odoo views and Flutter app development

### ✅ Completed Features

#### Odoo v18 Backend
- ✅ Core data models (Vehicle, Route, Customer, Territory, Visit)
- ✅ GPS tracking and location management
- ✅ Territory validation with geofencing
- ✅ Route optimization algorithms
- ✅ RESTful API controllers
- ✅ Mobile-specific API endpoints
- ✅ Security groups and access rights
- ✅ Extended Sale Order for van sales

#### Flutter Application
- ✅ Project structure and configuration
- ✅ App theme and styling system
- ✅ Core configuration files
- ✅ Dependency management setup

#### Documentation
- ✅ Comprehensive implementation plan
- ✅ Complete API documentation
- ✅ Deployment guide
- ✅ Project structure documentation

### 🔄 In Progress
- Odoo web views and user interface
- Flutter app core features implementation
- Database views and reports
- Testing and quality assurance

### 📋 Upcoming Features
- Interactive map integration
- Real-time synchronization
- Offline data handling
- Push notifications
- Camera integration
- Digital signature capture

## System Architecture

### Backend: Odoo v18 Module
- Van sales and distribution management
- Customer location management with GPS coordinates
- Sales territory restrictions with geofencing
- Vehicle tracking and route management
- Route planning with interactive maps
- Optimal route calculation algorithms
- Automatic customer visit scheduling

### Frontend: Flutter Application
- Cross-platform support (Android, iOS, Windows, Web)
- Real-time GPS tracking
- Interactive maps with customer locations
- Route optimization interface
- Offline capability for mobile users
- Real-time synchronization with Odoo backend

## Project Structure

```
van/
├── odoo_module/                 # Odoo v18 custom module
│   ├── van_sales/              # Main module directory
│   │   ├── models/             # Data models ✅
│   │   ├── views/              # XML views 🔄
│   │   ├── controllers/        # API controllers ✅
│   │   ├── security/           # Access rights ✅
│   │   ├── data/               # Demo/initial data
│   │   └── static/             # Static assets
│   └── requirements.txt        # Python dependencies
├── flutter_app/                # Flutter application
│   ├── lib/                    # Dart source code
│   │   ├── core/               # Core configuration ✅
│   │   ├── models/             # Data models
│   │   ├── services/           # API services
│   │   ├── screens/            # UI screens
│   │   ├── widgets/            # Reusable widgets
│   │   └── utils/              # Utilities
│   ├── android/                # Android-specific code
│   ├── ios/                    # iOS-specific code
│   ├── web/                    # Web-specific code
│   ├── windows/                # Windows-specific code
│   └── pubspec.yaml            # Flutter dependencies ✅
├── docs/                       # Documentation ✅
├── scripts/                    # Deployment scripts
└── docker/                     # Docker configurations
```

## Key Features

### 1. GPS Tracking & Geofencing
- Real-time vehicle location tracking
- Geofenced customer zones
- Territory-based sales restrictions
- Location history and analytics

### 2. Route Management
- Interactive map interface
- Optimal route calculation
- Customer visit scheduling
- Route performance analytics

### 3. Sales Management
- Customer management with GPS coordinates
- Order processing and tracking
- Inventory management
- Sales reporting and analytics

### 4. Mobile Capabilities
- Offline functionality
- Real-time synchronization
- Cross-platform compatibility
- Responsive design

## Technology Stack

- **Backend**: Odoo v18, PostgreSQL, Python
- **Frontend**: Flutter, Dart
- **Maps**: Google Maps API / OpenStreetMap
- **Database**: PostgreSQL
- **API**: REST API with JSON
- **Authentication**: Odoo session-based auth
- **Real-time**: WebSocket connections

## 🚀 Quick Start

### Prerequisites
- Odoo v18 development environment
- Flutter 3.10+ SDK
- PostgreSQL 12+
- Google Maps API key (optional)

### Backend Setup (Odoo)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd van
   ```

2. **Install Odoo dependencies**
   ```bash
   pip install -r odoo_module/requirements.txt
   ```

3. **Copy module to Odoo addons**
   ```bash
   cp -r odoo_module/van_sales /path/to/odoo/addons/
   ```

4. **Install the module**
   - Start Odoo server
   - Go to Apps menu
   - Search for "Van Sales"
   - Click Install

### Frontend Setup (Flutter)

1. **Navigate to Flutter app**
   ```bash
   cd flutter_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API endpoint**
   - Edit `lib/core/config/app_config.dart`
   - Set your Odoo server URL

4. **Run the app**
   ```bash
   flutter run
   ```

## 📚 Documentation

- **[Implementation Plan](docs/IMPLEMENTATION_PLAN.md)** - Detailed development roadmap
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference
- **[Deployment Guide](docs/DEPLOYMENT_GUIDE.md)** - Production deployment instructions

## 🏗️ Development Phases

### Phase 1: Foundation ✅
- [x] Project structure and setup
- [x] Core data models
- [x] Basic API endpoints
- [x] Security framework

### Phase 2: Backend Development 🔄
- [x] Advanced models and relationships
- [x] Route optimization algorithms
- [x] Territory management
- [ ] Web views and interface
- [ ] Reporting system

### Phase 3: Frontend Development 📋
- [x] App structure and configuration
- [ ] Authentication system
- [ ] Core UI components
- [ ] Map integration
- [ ] Offline functionality

### Phase 4: Integration & Testing 📋
- [ ] API integration
- [ ] End-to-end testing
- [ ] Performance optimization
- [ ] Security testing

### Phase 5: Deployment 📋
- [ ] Production setup
- [ ] Mobile app publishing
- [ ] Documentation finalization
- [ ] User training

## 🛠️ Technology Stack

### Backend
- **Framework**: Odoo v18
- **Language**: Python 3.8+
- **Database**: PostgreSQL
- **API**: REST with JSON
- **Maps**: Google Maps API / OpenStreetMap

### Frontend
- **Framework**: Flutter 3.10+
- **Language**: Dart
- **State Management**: Riverpod
- **Navigation**: GoRouter
- **Storage**: Hive + SQLite
- **Maps**: Google Maps Flutter

### DevOps
- **Deployment**: Docker, Nginx
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus, Grafana
- **Backup**: PostgreSQL dumps, File sync

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary software for van sales and distribution management.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**Built with ❤️ for efficient van sales management**
