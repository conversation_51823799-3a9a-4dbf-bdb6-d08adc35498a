# Van Sales System Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Van Sales and Distribution Management System in production environments.

## System Requirements

### Server Requirements (Odoo Backend)

#### Minimum Requirements
- **CPU**: 2 cores, 2.4 GHz
- **RAM**: 4 GB
- **Storage**: 50 GB SSD
- **OS**: Ubuntu 20.04 LTS or CentOS 8
- **Python**: 3.8+
- **PostgreSQL**: 12+

#### Recommended Requirements
- **CPU**: 4 cores, 3.0 GHz
- **RAM**: 8 GB
- **Storage**: 100 GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Python**: 3.10
- **PostgreSQL**: 14+

### Mobile Device Requirements

#### Android
- **OS**: Android 7.0 (API level 24) or higher
- **RAM**: 2 GB minimum, 4 GB recommended
- **Storage**: 100 MB for app, 500 MB for data
- **GPS**: Required
- **Camera**: Required for photo capture

#### iOS
- **OS**: iOS 12.0 or higher
- **Device**: iPhone 6s or newer
- **Storage**: 100 MB for app, 500 MB for data
- **GPS**: Required
- **Camera**: Required for photo capture

#### Windows Desktop
- **OS**: Windows 10 version 1903 or higher
- **RAM**: 4 GB minimum
- **Storage**: 200 MB for app
- **Internet**: Required for synchronization

## Pre-Deployment Checklist

### Infrastructure
- [ ] Server provisioned and accessible
- [ ] Domain name configured
- [ ] SSL certificate obtained
- [ ] Firewall rules configured
- [ ] Backup solution in place

### Dependencies
- [ ] PostgreSQL installed and configured
- [ ] Python 3.8+ installed
- [ ] Git installed
- [ ] Nginx/Apache web server configured
- [ ] SSL/TLS certificates configured

### API Keys and Services
- [ ] Google Maps API key obtained
- [ ] Push notification service configured
- [ ] Email service configured (SMTP)
- [ ] Backup storage configured

## Odoo Backend Deployment

### 1. System Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip python3-dev python3-venv
sudo apt install -y postgresql postgresql-contrib
sudo apt install -y git curl wget
sudo apt install -y nginx
sudo apt install -y certbot python3-certbot-nginx
```

### 2. PostgreSQL Setup

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE van_sales_db;
CREATE USER van_sales_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE van_sales_db TO van_sales_user;
ALTER USER van_sales_user CREATEDB;
\q
```

### 3. Odoo Installation

```bash
# Create odoo user
sudo adduser --system --home=/opt/odoo --group odoo

# Download Odoo v18
sudo git clone https://github.com/odoo/odoo.git /opt/odoo/odoo18 --depth 1 --branch 18.0

# Create virtual environment
sudo -u odoo python3 -m venv /opt/odoo/venv

# Install Python dependencies
sudo -u odoo /opt/odoo/venv/bin/pip install -r /opt/odoo/odoo18/requirements.txt
sudo -u odoo /opt/odoo/venv/bin/pip install geopy folium requests
```

### 4. Van Sales Module Installation

```bash
# Create custom addons directory
sudo mkdir -p /opt/odoo/custom-addons

# Copy van_sales module
sudo cp -r /path/to/van_sales /opt/odoo/custom-addons/

# Set permissions
sudo chown -R odoo:odoo /opt/odoo/custom-addons
```

### 5. Odoo Configuration

Create `/opt/odoo/odoo.conf`:

```ini
[options]
admin_passwd = admin_master_password
db_host = localhost
db_port = 5432
db_user = van_sales_user
db_password = secure_password
addons_path = /opt/odoo/odoo18/addons,/opt/odoo/custom-addons
logfile = /var/log/odoo/odoo.log
log_level = info
workers = 4
max_cron_threads = 2
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
proxy_mode = True
xmlrpc_port = 8069
longpolling_port = 8072
```

### 6. Systemd Service

Create `/etc/systemd/system/odoo.service`:

```ini
[Unit]
Description=Odoo
Documentation=http://www.odoo.com
After=network.target postgresql.service

[Service]
Type=simple
SyslogIdentifier=odoo
PermissionsStartOnly=true
User=odoo
Group=odoo
ExecStart=/opt/odoo/venv/bin/python /opt/odoo/odoo18/odoo-bin -c /opt/odoo/odoo.conf
StandardOutput=journal+console
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 7. Start Odoo Service

```bash
# Create log directory
sudo mkdir -p /var/log/odoo
sudo chown odoo:odoo /var/log/odoo

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable odoo
sudo systemctl start odoo

# Check status
sudo systemctl status odoo
```

### 8. Nginx Configuration

Create `/etc/nginx/sites-available/van-sales`:

```nginx
upstream odoo {
    server 127.0.0.1:8069;
}

upstream odoochat {
    server 127.0.0.1:8072;
}

map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    access_log /var/log/nginx/odoo.access.log;
    error_log /var/log/nginx/odoo.error.log;

    proxy_read_timeout 720s;
    proxy_connect_timeout 720s;
    proxy_send_timeout 720s;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;

    location / {
        proxy_redirect off;
        proxy_pass http://odoo;
    }

    location /longpolling {
        proxy_pass http://odoochat;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location ~* /web/static/ {
        proxy_cache_valid 200 90m;
        proxy_buffering on;
        expires 864000;
        proxy_pass http://odoo;
    }

    gzip on;
    gzip_types text/css text/scss text/plain text/xml application/xml application/json application/javascript;
    gzip_min_length 1000;

    client_max_body_size 200m;
}
```

### 9. SSL Certificate

```bash
# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Enable nginx site
sudo ln -s /etc/nginx/sites-available/van-sales /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 10. Database Initialization

```bash
# Access Odoo web interface
# Navigate to https://your-domain.com
# Create database: van_sales_db
# Install van_sales module
```

## Flutter App Deployment

### 1. Build Configuration

Update `flutter_app/lib/core/config/app_config.dart`:

```dart
static String get baseUrl => 'https://your-domain.com';
static String get database => 'van_sales_db';
static String get googleMapsApiKey => 'your_google_maps_api_key';
```

### 2. Android Deployment

```bash
# Build APK
cd flutter_app
flutter build apk --release

# Build App Bundle (for Play Store)
flutter build appbundle --release

# Sign APK (if not using Play Store)
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore your-keystore.jks app-release-unsigned.apk your-key-alias
```

### 3. iOS Deployment

```bash
# Build iOS app
flutter build ios --release

# Archive in Xcode
# Upload to App Store Connect
```

### 4. Windows Deployment

```bash
# Build Windows app
flutter build windows --release

# Create installer using Inno Setup or similar
```

### 5. Web Deployment

```bash
# Build web app
flutter build web --release

# Deploy to web server
sudo cp -r build/web/* /var/www/van-sales-app/
```

## Post-Deployment Configuration

### 1. Odoo Module Configuration

1. **Install van_sales module**
2. **Configure company settings**
3. **Set up territories and boundaries**
4. **Create vehicle records**
5. **Import customer data**
6. **Configure user permissions**

### 2. System Integration

1. **Configure Google Maps API**
2. **Set up push notifications**
3. **Configure email settings**
4. **Set up backup procedures**
5. **Configure monitoring**

### 3. User Setup

1. **Create user accounts**
2. **Assign vehicles to drivers**
3. **Set up territories**
4. **Configure permissions**
5. **Provide training**

## Monitoring and Maintenance

### 1. System Monitoring

```bash
# Monitor Odoo service
sudo systemctl status odoo
sudo journalctl -u odoo -f

# Monitor database
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Monitor disk space
df -h

# Monitor memory usage
free -h
```

### 2. Log Management

```bash
# Odoo logs
tail -f /var/log/odoo/odoo.log

# Nginx logs
tail -f /var/log/nginx/odoo.access.log
tail -f /var/log/nginx/odoo.error.log

# System logs
journalctl -f
```

### 3. Backup Procedures

```bash
# Database backup
sudo -u postgres pg_dump van_sales_db > backup_$(date +%Y%m%d_%H%M%S).sql

# File backup
tar -czf odoo_files_$(date +%Y%m%d_%H%M%S).tar.gz /opt/odoo/custom-addons

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backup/van_sales"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
sudo -u postgres pg_dump van_sales_db | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /opt/odoo/custom-addons

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

### 4. Update Procedures

```bash
# Update Odoo
sudo systemctl stop odoo
sudo -u odoo git -C /opt/odoo/odoo18 pull
sudo systemctl start odoo

# Update van_sales module
# Copy new module files
sudo systemctl restart odoo
# Update module in Odoo interface
```

## Troubleshooting

### Common Issues

1. **Odoo won't start**: Check logs, database connection, permissions
2. **Module not loading**: Check addons path, file permissions
3. **API errors**: Check authentication, CORS settings
4. **Mobile app sync issues**: Check network, API endpoints
5. **GPS not working**: Check permissions, location services

### Performance Optimization

1. **Database tuning**: Optimize PostgreSQL configuration
2. **Odoo workers**: Adjust worker count based on CPU cores
3. **Nginx caching**: Enable static file caching
4. **Database indexing**: Add indexes for frequently queried fields
5. **Mobile app**: Optimize sync frequency and data size

This deployment guide provides a comprehensive approach to setting up the Van Sales system in production. Adjust configurations based on your specific requirements and infrastructure.
