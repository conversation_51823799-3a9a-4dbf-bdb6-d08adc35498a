# Van Sales API Documentation

## Overview

The Van Sales API provides RESTful endpoints for managing van sales operations, including vehicle tracking, route management, customer visits, and sales orders. The API is built on Odoo v18 and supports both web and mobile applications.

## Base URL

```
https://your-odoo-server.com/api/van
```

## Authentication

The API uses session-based authentication. Users must authenticate with their Odoo credentials.

### Mobile Login

```http
POST /api/mobile/van/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "vehicle_id": 5,
    "vehicle_name": "Van-001",
    "session_id": "abc123...",
    "permissions": {
      "can_create_orders": true,
      "can_manage_routes": false,
      "can_view_reports": false
    }
  },
  "error": null,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Standard Response Format

All API responses follow this format:

```json
{
  "success": boolean,
  "data": object|array|null,
  "error": string|null,
  "timestamp": "ISO 8601 datetime"
}
```

## Vehicle Management

### Get Vehicles

```http
GET /api/van/vehicles
Authorization: Session
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Van-001",
      "license_plate": "ABC-123",
      "driver_id": 5,
      "driver_name": "John Doe",
      "state": "available",
      "current_latitude": 40.7128,
      "current_longitude": -74.0060,
      "last_location_update": "2024-01-15T10:25:00Z"
    }
  ]
}
```

### Update Vehicle Location

```http
POST /api/van/vehicle/{vehicle_id}/location
Authorization: Session
Content-Type: application/json

{
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Location updated successfully"
  }
}
```

## Route Management

### Get Routes

```http
GET /api/van/routes?date_from=2024-01-15&vehicle_id=1&state=planned
Authorization: Session
```

**Query Parameters:**
- `date_from` (optional): Filter routes from date (YYYY-MM-DD)
- `date_to` (optional): Filter routes to date (YYYY-MM-DD)
- `vehicle_id` (optional): Filter by vehicle ID
- `state` (optional): Filter by route state (draft, planned, in_progress, completed, cancelled)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Route-001",
      "code": "VR001",
      "planned_date": "2024-01-15",
      "state": "planned",
      "vehicle_id": 1,
      "vehicle_name": "Van-001",
      "driver_name": "John Doe",
      "total_visits": 8,
      "completed_visits": 0,
      "total_distance": 45.2,
      "estimated_duration": 6.5
    }
  ]
}
```

### Get Route Details

```http
GET /api/van/route/{route_id}
Authorization: Session
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Route-001",
    "code": "VR001",
    "planned_date": "2024-01-15",
    "state": "planned",
    "vehicle_id": 1,
    "vehicle_name": "Van-001",
    "driver_name": "John Doe",
    "start_location": "Warehouse",
    "start_latitude": 40.7128,
    "start_longitude": -74.0060,
    "total_distance": 45.2,
    "estimated_duration": 6.5,
    "optimized_route": {
      "total_distance": 45.2,
      "total_duration": 390,
      "waypoints": [...]
    },
    "visits": [
      {
        "id": 1,
        "name": "Visit-001",
        "sequence": 1,
        "customer_id": 10,
        "customer_name": "ABC Store",
        "customer_latitude": 40.7589,
        "customer_longitude": -73.9851,
        "visit_date": "2024-01-15",
        "planned_time": "2024-01-15T09:00:00Z",
        "state": "planned",
        "visit_type": "sales",
        "purpose": "Regular sales visit",
        "notes": ""
      }
    ],
    "notes": "Morning route",
    "special_instructions": "Avoid highway during rush hour"
  }
}
```

### Optimize Route

```http
POST /api/van/route/{route_id}/optimize
Authorization: Session
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Route optimized successfully"
  }
}
```

## Customer Management

### Get Customers

```http
GET /api/van/customers?territory_id=1&search=ABC&limit=50
Authorization: Session
```

**Query Parameters:**
- `territory_id` (optional): Filter by territory ID
- `search` (optional): Search by customer name
- `limit` (optional): Limit results (default: 100)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "ABC Store",
      "latitude": 40.7589,
      "longitude": -73.9851,
      "address": "123 Main St, New York, NY",
      "territory_id": 1,
      "territory_name": "Manhattan North",
      "customer_type": "retail",
      "priority": "high",
      "preferred_visit_time": "morning",
      "visit_frequency": "weekly",
      "last_visit_date": "2024-01-08T14:30:00Z",
      "total_visits": 25,
      "total_sales": 15750.00
    }
  ]
}
```

## Territory Management

### Get Territories

```http
GET /api/van/territories
Authorization: Session
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Manhattan North",
      "code": "MN001",
      "territory_type": "circle",
      "center_latitude": 40.7831,
      "center_longitude": -73.9712,
      "radius": 5.0,
      "boundary_coordinates": null,
      "color": "#FF0000",
      "customer_count": 45,
      "manager_name": "Jane Smith"
    }
  ]
}
```

### Validate Territory Location

```http
POST /api/van/territory/{territory_id}/validate
Authorization: Session
Content-Type: application/json

{
  "latitude": 40.7589,
  "longitude": -73.9851
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "territory_name": "Manhattan North",
    "message": "Location is within territory"
  }
}
```

## Mobile-Specific Endpoints

### Mobile Data Sync

```http
GET /api/mobile/van/sync
Authorization: Session
```

**Response:**
```json
{
  "success": true,
  "data": {
    "vehicle": {
      "id": 1,
      "name": "Van-001",
      "license_plate": "ABC-123",
      "state": "available"
    },
    "routes": [...],
    "customers": [...],
    "territories": [...],
    "products": [...],
    "sync_timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Start Visit

```http
POST /api/mobile/van/visit/{visit_id}/start
Authorization: Session
Content-Type: application/json

{
  "latitude": 40.7589,
  "longitude": -73.9851
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Visit started successfully",
    "visit_id": 1,
    "state": "in_progress"
  }
}
```

### Complete Visit

```http
POST /api/mobile/van/visit/{visit_id}/complete
Authorization: Session
Content-Type: application/json

{
  "notes": "Customer was satisfied with products",
  "customer_feedback": "Requested more variety next time",
  "visit_result": "successful",
  "next_visit_date": "2024-01-22",
  "follow_up_required": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Visit completed successfully",
    "visit_id": 1,
    "state": "completed"
  }
}
```

### Create Sales Order

```http
POST /api/mobile/van/order/create
Authorization: Session
Content-Type: application/json

{
  "customer_id": 1,
  "visit_id": 1,
  "delivery_method": "immediate",
  "payment_method": "cash",
  "latitude": 40.7589,
  "longitude": -73.9851,
  "order_lines": [
    {
      "product_id": 10,
      "quantity": 5,
      "price_unit": 25.00
    },
    {
      "product_id": 15,
      "quantity": 2,
      "price_unit": 50.00
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Order created successfully",
    "order_id": 123,
    "order_name": "SO001",
    "amount_total": 225.00
  }
}
```

## Error Handling

### Error Response Format

```json
{
  "success": false,
  "data": null,
  "error": "Error message description",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Common HTTP Status Codes

- `200 OK`: Successful request
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

### Common Error Messages

- `"Authentication required"`: User not logged in
- `"Invalid credentials"`: Wrong username/password
- `"No van sales access"`: User lacks van sales permissions
- `"Vehicle not found"`: Invalid vehicle ID
- `"Route not found"`: Invalid route ID
- `"Latitude and longitude required"`: Missing GPS coordinates

## Rate Limiting

- **General API**: 100 requests per minute per user
- **Location Updates**: 1 request per 30 seconds per vehicle
- **Sync Endpoint**: 1 request per 5 minutes per user

## Data Formats

### Date/Time Format
All dates and times use ISO 8601 format: `YYYY-MM-DDTHH:MM:SSZ`

### Coordinate Format
- Latitude: Decimal degrees (-90 to 90)
- Longitude: Decimal degrees (-180 to 180)
- Precision: 6 decimal places

### Currency Format
All monetary values are in the company's base currency with 2 decimal places.
