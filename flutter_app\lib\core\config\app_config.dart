import 'package:shared_preferences/shared_preferences.dart';

class AppConfig {
  static late SharedPreferences _prefs;
  
  // API Configuration
  static String get baseUrl => _getString('base_url', 'https://your-odoo-server.com');
  static String get apiVersion => _getString('api_version', 'v1');
  static String get database => _getString('database', 'van_sales_db');
  
  // App Settings
  static bool get enableOfflineMode => _getBool('enable_offline_mode', true);
  static bool get enableLocationTracking => _getBool('enable_location_tracking', true);
  static bool get enableNotifications => _getBool('enable_notifications', true);
  static int get syncInterval => _getInt('sync_interval', 300); // 5 minutes
  static int get locationUpdateInterval => _getInt('location_update_interval', 30); // 30 seconds
  
  // Map Settings
  static String get mapProvider => _getString('map_provider', 'google');
  static String get googleMapsApiKey => _getString('google_maps_api_key', '');
  static double get defaultMapZoom => _getDouble('default_map_zoom', 15.0);
  
  // Cache Settings
  static int get maxCacheSize => _getInt('max_cache_size', 100); // MB
  static int get cacheExpiryDays => _getInt('cache_expiry_days', 7);
  
  // Security Settings
  static bool get enableBiometricAuth => _getBool('enable_biometric_auth', false);
  static int get sessionTimeoutMinutes => _getInt('session_timeout_minutes', 480); // 8 hours
  
  static Future<void> load() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  static String _getString(String key, String defaultValue) {
    return _prefs.getString(key) ?? defaultValue;
  }
  
  static bool _getBool(String key, bool defaultValue) {
    return _prefs.getBool(key) ?? defaultValue;
  }
  
  static int _getInt(String key, int defaultValue) {
    return _prefs.getInt(key) ?? defaultValue;
  }
  
  static double _getDouble(String key, double defaultValue) {
    return _prefs.getDouble(key) ?? defaultValue;
  }
  
  // Setters
  static Future<void> setBaseUrl(String url) async {
    await _prefs.setString('base_url', url);
  }
  
  static Future<void> setDatabase(String db) async {
    await _prefs.setString('database', db);
  }
  
  static Future<void> setEnableOfflineMode(bool enabled) async {
    await _prefs.setBool('enable_offline_mode', enabled);
  }
  
  static Future<void> setEnableLocationTracking(bool enabled) async {
    await _prefs.setBool('enable_location_tracking', enabled);
  }
  
  static Future<void> setEnableNotifications(bool enabled) async {
    await _prefs.setBool('enable_notifications', enabled);
  }
  
  static Future<void> setSyncInterval(int seconds) async {
    await _prefs.setInt('sync_interval', seconds);
  }
  
  static Future<void> setLocationUpdateInterval(int seconds) async {
    await _prefs.setInt('location_update_interval', seconds);
  }
  
  static Future<void> setGoogleMapsApiKey(String key) async {
    await _prefs.setString('google_maps_api_key', key);
  }
  
  static Future<void> setDefaultMapZoom(double zoom) async {
    await _prefs.setDouble('default_map_zoom', zoom);
  }
  
  static Future<void> setEnableBiometricAuth(bool enabled) async {
    await _prefs.setBool('enable_biometric_auth', enabled);
  }
  
  static Future<void> reset() async {
    await _prefs.clear();
  }
}
