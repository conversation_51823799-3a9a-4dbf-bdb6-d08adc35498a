import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../models/user.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

// Auth state
class AuthState {
  final User? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState()) {
    _loadSavedUser();
  }

  final ApiService _apiService = ApiService.instance;

  void _loadSavedUser() {
    final savedUser = StorageService.getCurrentUser();
    if (savedUser != null && _apiService.isAuthenticated) {
      state = state.copyWith(
        user: savedUser,
        isAuthenticated: true,
      );
    }
  }

  Future<bool> login(String username, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.login(username, password);

      if (response.success && response.data != null) {
        final userData = response.data!;
        
        final user = User(
          id: userData['user_id'] as int,
          name: userData['name'] as String,
          email: userData['email'] as String,
          vehicleId: userData['vehicle_id'] as int?,
          vehicleName: userData['vehicle_name'] as String?,
          sessionId: userData['session_id'] as String?,
          permissions: UserPermissions.fromJson(
            userData['permissions'] as Map<String, dynamic>,
          ),
          lastLogin: DateTime.now(),
        );

        await StorageService.saveUser(user);

        state = state.copyWith(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );

        debugPrint('Login successful for user: ${user.name}');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'Login failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Login error: $e',
      );
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      await _apiService.logout();
    } catch (e) {
      debugPrint('Logout error: $e');
    }

    await StorageService.clearUser();
    
    state = const AuthState();
    debugPrint('User logged out');
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void updateUser(User user) {
    state = state.copyWith(user: user);
    StorageService.saveUser(user);
  }

  bool get isLoggedIn => state.isAuthenticated && state.user != null;
  User? get currentUser => state.user;
  bool get hasVehicle => state.user?.vehicleId != null;
  UserPermissions? get permissions => state.user?.permissions;
}

// Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Convenience providers
final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider).user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final userPermissionsProvider = Provider<UserPermissions?>((ref) {
  return ref.watch(authProvider).user?.permissions;
});

final hasVehicleProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.vehicleId != null;
});

final currentVehicleIdProvider = Provider<int?>((ref) {
  return ref.watch(currentUserProvider)?.vehicleId;
});
