from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import json
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class VanRoute(models.Model):
    _name = 'van.route'
    _description = 'Van Sales Route'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'planned_date desc, name'

    name = fields.Char(
        string='Route Name',
        required=True,
        tracking=True
    )
    code = fields.Char(
        string='Route Code',
        required=True,
        copy=False
    )

    # Vehicle and Driver
    vehicle_id = fields.Many2one(
        'van.vehicle',
        string='Vehicle',
        required=True,
        tracking=True
    )
    driver_id = fields.Many2one(
        'res.users',
        string='Driver',
        related='vehicle_id.driver_id',
        store=True,
        readonly=True
    )

    # Route Planning
    planned_date = fields.Date(
        string='Planned Date',
        required=True,
        default=fields.Date.today,
        tracking=True
    )
    start_time = fields.Datetime(
        string='Start Time',
        tracking=True
    )
    end_time = fields.Datetime(
        string='End Time',
        tracking=True
    )

    # Route Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    # Route Details
    start_location = fields.Char(
        string='Start Location',
        default='Warehouse'
    )
    start_latitude = fields.Float(
        string='Start Latitude',
        digits=(10, 6)
    )
    start_longitude = fields.Float(
        string='Start Longitude',
        digits=(10, 6)
    )

    end_location = fields.Char(
        string='End Location',
        default='Warehouse'
    )
    end_latitude = fields.Float(
        string='End Latitude',
        digits=(10, 6)
    )
    end_longitude = fields.Float(
        string='End Longitude',
        digits=(10, 6)
    )

    # Route Optimization
    optimized_route = fields.Text(
        string='Optimized Route',
        help='JSON data containing optimized route information'
    )
    total_distance = fields.Float(
        string='Total Distance (km)',
        compute='_compute_route_metrics',
        store=True
    )
    estimated_duration = fields.Float(
        string='Estimated Duration (hours)',
        compute='_compute_route_metrics',
        store=True
    )
    actual_distance = fields.Float(
        string='Actual Distance (km)'
    )
    actual_duration = fields.Float(
        string='Actual Duration (hours)'
    )

    # Visits and Customers
    visit_ids = fields.One2many(
        'van.visit',
        'route_id',
        string='Planned Visits'
    )
    customer_ids = fields.Many2many(
        'van.customer',
        string='Customers',
        compute='_compute_customers',
        store=True
    )

    # Territory
    territory_ids = fields.Many2many(
        'van.territory',
        string='Territories',
        compute='_compute_territories',
        store=True
    )

    # Statistics
    total_visits = fields.Integer(
        string='Total Visits',
        compute='_compute_statistics'
    )
    completed_visits = fields.Integer(
        string='Completed Visits',
        compute='_compute_statistics'
    )
    pending_visits = fields.Integer(
        string='Pending Visits',
        compute='_compute_statistics'
    )
    completion_rate = fields.Float(
        string='Completion Rate (%)',
        compute='_compute_statistics'
    )

    # Sales Information
    total_sales = fields.Monetary(
        string='Total Sales',
        compute='_compute_sales_metrics',
        currency_field='currency_id'
    )
    total_orders = fields.Integer(
        string='Total Orders',
        compute='_compute_sales_metrics'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )

    # Notes and Instructions
    notes = fields.Text(
        string='Route Notes'
    )
    special_instructions = fields.Text(
        string='Special Instructions'
    )

    @api.depends('visit_ids', 'visit_ids.customer_id')
    def _compute_customers(self):
        for route in self:
            route.customer_ids = route.visit_ids.mapped('customer_id')

    @api.depends('customer_ids', 'customer_ids.territory_id')
    def _compute_territories(self):
        for route in self:
            route.territory_ids = route.customer_ids.mapped('territory_id')

    @api.depends('visit_ids')
    def _compute_statistics(self):
        for route in self:
            route.total_visits = len(route.visit_ids)
            route.completed_visits = len(route.visit_ids.filtered(lambda v: v.state == 'completed'))
            route.pending_visits = route.total_visits - route.completed_visits

            if route.total_visits > 0:
                route.completion_rate = (route.completed_visits / route.total_visits) * 100
            else:
                route.completion_rate = 0.0

    @api.depends('visit_ids', 'visit_ids.sales_order_ids')
    def _compute_sales_metrics(self):
        for route in self:
            orders = route.visit_ids.mapped('sales_order_ids').filtered(
                lambda o: o.state in ['sale', 'done']
            )
            route.total_orders = len(orders)
            route.total_sales = sum(orders.mapped('amount_total'))

    @api.depends('visit_ids', 'optimized_route')
    def _compute_route_metrics(self):
        for route in self:
            if route.optimized_route:
                try:
                    route_data = json.loads(route.optimized_route)
                    route.total_distance = route_data.get('total_distance', 0.0)
                    route.estimated_duration = route_data.get('total_duration', 0.0) / 60  # Convert to hours
                except (json.JSONDecodeError, Exception):
                    route.total_distance = 0.0
                    route.estimated_duration = 0.0
            else:
                route.total_distance = 0.0
                route.estimated_duration = 0.0

    @api.model
    def create(self, vals):
        """Generate route code if not provided"""
        if not vals.get('code'):
            vals['code'] = self.env['ir.sequence'].next_by_code('van.route') or 'VR001'
        return super().create(vals)

    def action_plan_route(self):
        """Plan and optimize the route"""
        self.ensure_one()
        if not self.visit_ids:
            raise UserError(_('Cannot plan route without visits. Please add visits first.'))

        # Optimize route using external service or algorithm
        optimized_data = self._optimize_route()

        self.write({
            'optimized_route': json.dumps(optimized_data),
            'state': 'planned'
        })

        # Update visit sequence based on optimization
        self._update_visit_sequence(optimized_data)

        self.message_post(
            body=_('Route has been planned and optimized.'),
            message_type='notification'
        )

    def action_start_route(self):
        """Start the route execution"""
        self.ensure_one()
        if self.state != 'planned':
            raise UserError(_('Route must be planned before starting.'))

        self.write({
            'state': 'in_progress',
            'start_time': fields.Datetime.now()
        })

        # Update vehicle status
        self.vehicle_id.action_set_on_route()

        self.message_post(
            body=_('Route execution started.'),
            message_type='notification'
        )

    def action_complete_route(self):
        """Complete the route execution"""
        self.ensure_one()
        if self.state != 'in_progress':
            raise UserError(_('Route must be in progress to complete.'))

        self.write({
            'state': 'completed',
            'end_time': fields.Datetime.now()
        })

        # Calculate actual duration
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
            self.actual_duration = duration.total_seconds() / 3600  # Convert to hours

        # Update vehicle status
        self.vehicle_id.action_set_available()

        self.message_post(
            body=_('Route execution completed.'),
            message_type='notification'
        )

    def action_cancel_route(self):
        """Cancel the route"""
        self.ensure_one()
        if self.state == 'completed':
            raise UserError(_('Cannot cancel a completed route.'))

        self.state = 'cancelled'

        # Update vehicle status if route was in progress
        if self.state == 'in_progress':
            self.vehicle_id.action_set_available()

        self.message_post(
            body=_('Route has been cancelled.'),
            message_type='notification'
        )

    def _optimize_route(self):
        """Optimize route using distance matrix and TSP algorithm"""
        # This is a simplified optimization algorithm
        # In production, you would use Google Maps API or similar service

        visits = self.visit_ids.sorted('sequence')
        if not visits:
            return {'total_distance': 0, 'total_duration': 0, 'waypoints': []}

        # Simple optimization: sort by geographical proximity
        optimized_visits = self._sort_visits_by_proximity(visits)

        # Calculate total distance and duration
        total_distance = 0
        total_duration = 0
        waypoints = []

        prev_lat, prev_lng = self.start_latitude or 0, self.start_longitude or 0

        for visit in optimized_visits:
            if visit.customer_id.latitude and visit.customer_id.longitude:
                # Calculate distance (simplified using Euclidean distance)
                distance = self._calculate_distance(
                    prev_lat, prev_lng,
                    visit.customer_id.latitude, visit.customer_id.longitude
                )
                total_distance += distance
                total_duration += distance * 2  # Assume 2 minutes per km

                waypoints.append({
                    'visit_id': visit.id,
                    'customer_name': visit.customer_id.name,
                    'latitude': visit.customer_id.latitude,
                    'longitude': visit.customer_id.longitude,
                    'sequence': len(waypoints) + 1
                })

                prev_lat, prev_lng = visit.customer_id.latitude, visit.customer_id.longitude

        return {
            'total_distance': total_distance,
            'total_duration': total_duration,
            'waypoints': waypoints
        }

    def _sort_visits_by_proximity(self, visits):
        """Sort visits by geographical proximity using nearest neighbor algorithm"""
        if not visits:
            return visits

        sorted_visits = []
        remaining_visits = list(visits)

        # Start with the first visit
        current_visit = remaining_visits.pop(0)
        sorted_visits.append(current_visit)
        current_lat = current_visit.customer_id.latitude
        current_lng = current_visit.customer_id.longitude

        # Find nearest neighbor for each subsequent visit
        while remaining_visits:
            nearest_visit = None
            min_distance = float('inf')

            for visit in remaining_visits:
                if visit.customer_id.latitude and visit.customer_id.longitude:
                    distance = self._calculate_distance(
                        current_lat, current_lng,
                        visit.customer_id.latitude, visit.customer_id.longitude
                    )
                    if distance < min_distance:
                        min_distance = distance
                        nearest_visit = visit

            if nearest_visit:
                remaining_visits.remove(nearest_visit)
                sorted_visits.append(nearest_visit)
                current_lat = nearest_visit.customer_id.latitude
                current_lng = nearest_visit.customer_id.longitude
            else:
                # If no visit with coordinates found, add the first remaining
                sorted_visits.append(remaining_visits.pop(0))

        return sorted_visits

    def _calculate_distance(self, lat1, lng1, lat2, lng2):
        """Calculate distance between two points using Haversine formula"""
        from math import radians, sin, cos, sqrt, atan2

        # Convert to radians
        lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])

        # Haversine formula
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))

        # Earth's radius in kilometers
        r = 6371

        return r * c

    def _update_visit_sequence(self, optimized_data):
        """Update visit sequence based on optimization results"""
        waypoints = optimized_data.get('waypoints', [])

        for waypoint in waypoints:
            visit = self.env['van.visit'].browse(waypoint['visit_id'])
            if visit:
                visit.sequence = waypoint['sequence']

    def action_view_map(self):
        """Open route map view"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Route Map'),
            'res_model': 'van.route',
            'res_id': self.id,
            'view_mode': 'form',
            'view_id': self.env.ref('van_sales.van_route_map_view').id,
            'target': 'new'
        }
