import json
import logging
from datetime import datetime, timedelta

from odoo import http, fields, _
from odoo.http import request
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class VanMobileAPI(http.Controller):
    """Mobile-specific API endpoints for van sales app"""
    
    def _authenticate_user(self):
        """Authenticate user and return user record"""
        if not request.session.uid:
            return None
        return request.env.user
    
    def _json_response(self, data=None, error=None, status=200):
        """Return standardized JSON response"""
        response_data = {
            'success': error is None,
            'data': data,
            'error': error,
            'timestamp': fields.Datetime.now().isoformat()
        }
        return request.make_response(
            json.dumps(response_data, default=str),
            headers=[('Content-Type', 'application/json')],
            status=status
        )
    
    @http.route('/api/mobile/van/login', type='http', auth='none', methods=['POST'], csrf=False)
    def mobile_login(self, **kwargs):
        """Mobile app login"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return self._json_response(error='Username and password required', status=400)
            
            # Authenticate user
            uid = request.session.authenticate(request.session.db, username, password)
            if not uid:
                return self._json_response(error='Invalid credentials', status=401)
            
            user = request.env.user
            
            # Check if user has van sales access
            if not user.has_group('van_sales.group_van_sales_user'):
                return self._json_response(error='No van sales access', status=403)
            
            # Get user's assigned vehicle
            vehicle = request.env['van.vehicle'].search([('driver_id', '=', user.id)], limit=1)
            
            user_data = {
                'user_id': user.id,
                'name': user.name,
                'email': user.email,
                'vehicle_id': vehicle.id if vehicle else None,
                'vehicle_name': vehicle.name if vehicle else None,
                'session_id': request.session.sid,
                'permissions': {
                    'can_create_orders': user.has_group('van_sales.group_van_sales_user'),
                    'can_manage_routes': user.has_group('van_sales.group_van_sales_manager'),
                    'can_view_reports': user.has_group('van_sales.group_van_sales_manager')
                }
            }
            
            return self._json_response(data=user_data)
            
        except Exception as e:
            _logger.error('Mobile login error: %s', str(e))
            return self._json_response(error=str(e), status=500)
    
    @http.route('/api/mobile/van/sync', type='http', auth='user', methods=['GET'], csrf=False)
    def mobile_sync(self, **kwargs):
        """Sync data for mobile app"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)
            
            # Get user's vehicle
            vehicle = request.env['van.vehicle'].search([('driver_id', '=', user.id)], limit=1)
            if not vehicle:
                return self._json_response(error='No vehicle assigned', status=404)
            
            # Get today's routes
            today = fields.Date.today()
            routes = request.env['van.route'].search([
                ('vehicle_id', '=', vehicle.id),
                ('planned_date', '=', today),
                ('state', 'in', ['planned', 'in_progress'])
            ])
            
            sync_data = {
                'vehicle': {
                    'id': vehicle.id,
                    'name': vehicle.name,
                    'license_plate': vehicle.license_plate,
                    'state': vehicle.state
                },
                'routes': [],
                'customers': [],
                'territories': [],
                'products': [],
                'sync_timestamp': fields.Datetime.now().isoformat()
            }
            
            # Routes data
            for route in routes:
                route_data = {
                    'id': route.id,
                    'name': route.name,
                    'code': route.code,
                    'state': route.state,
                    'planned_date': route.planned_date.isoformat(),
                    'visits': []
                }
                
                # Visits data
                for visit in route.visit_ids:
                    visit_data = {
                        'id': visit.id,
                        'name': visit.name,
                        'sequence': visit.sequence,
                        'state': visit.state,
                        'visit_type': visit.visit_type,
                        'customer_id': visit.customer_id.id,
                        'customer_name': visit.customer_id.name,
                        'customer_latitude': visit.customer_latitude,
                        'customer_longitude': visit.customer_longitude,
                        'planned_time': visit.planned_time.isoformat() if visit.planned_time else None,
                        'purpose': visit.purpose,
                        'notes': visit.notes
                    }
                    route_data['visits'].append(visit_data)
                
                sync_data['routes'].append(route_data)
            
            # Customers data (in assigned territories)
            if vehicle.territory_ids:
                customers = request.env['van.customer'].search([
                    ('territory_id', 'in', vehicle.territory_ids.ids),
                    ('active', '=', True)
                ])
                
                for customer in customers:
                    customer_data = {
                        'id': customer.id,
                        'name': customer.name,
                        'latitude': customer.latitude,
                        'longitude': customer.longitude,
                        'address': customer.address,
                        'customer_type': customer.customer_type,
                        'priority': customer.priority,
                        'preferred_visit_time': customer.preferred_visit_time,
                        'contact_person': customer.contact_person,
                        'contact_phone': customer.contact_phone,
                        'delivery_instructions': customer.delivery_instructions
                    }
                    sync_data['customers'].append(customer_data)
                
                # Territories data
                for territory in vehicle.territory_ids:
                    territory_data = {
                        'id': territory.id,
                        'name': territory.name,
                        'territory_type': territory.territory_type,
                        'center_latitude': territory.center_latitude,
                        'center_longitude': territory.center_longitude,
                        'radius': territory.radius,
                        'boundary_coordinates': json.loads(territory.boundary_coordinates) if territory.boundary_coordinates else None,
                        'color': territory.color
                    }
                    sync_data['territories'].append(territory_data)
            
            # Products data (available on van)
            products = request.env['product.product'].search([
                ('sale_ok', '=', True),
                ('active', '=', True)
            ], limit=100)  # Limit for mobile performance
            
            for product in products:
                product_data = {
                    'id': product.id,
                    'name': product.name,
                    'default_code': product.default_code,
                    'list_price': product.list_price,
                    'uom_name': product.uom_id.name,
                    'image_url': f'/web/image/product.product/{product.id}/image_128' if product.image_128 else None
                }
                sync_data['products'].append(product_data)
            
            return self._json_response(data=sync_data)
            
        except Exception as e:
            _logger.error('Mobile sync error: %s', str(e))
            return self._json_response(error=str(e), status=500)
    
    @http.route('/api/mobile/van/visit/<int:visit_id>/start', type='http', auth='user', methods=['POST'], csrf=False)
    def start_visit(self, visit_id, **kwargs):
        """Start a visit from mobile app"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)
            
            data = json.loads(request.httprequest.data.decode('utf-8'))
            latitude = data.get('latitude')
            longitude = data.get('longitude')
            
            visit = request.env['van.visit'].browse(visit_id)
            if not visit.exists():
                return self._json_response(error='Visit not found', status=404)
            
            # Update location if provided
            if latitude and longitude:
                visit.action_update_location(latitude, longitude)
            
            visit.action_start_visit()
            
            return self._json_response(data={
                'message': 'Visit started successfully',
                'visit_id': visit.id,
                'state': visit.state
            })
            
        except Exception as e:
            _logger.error('Error starting visit: %s', str(e))
            return self._json_response(error=str(e), status=500)
    
    @http.route('/api/mobile/van/visit/<int:visit_id>/complete', type='http', auth='user', methods=['POST'], csrf=False)
    def complete_visit(self, visit_id, **kwargs):
        """Complete a visit from mobile app"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)
            
            data = json.loads(request.httprequest.data.decode('utf-8'))
            
            visit = request.env['van.visit'].browse(visit_id)
            if not visit.exists():
                return self._json_response(error='Visit not found', status=404)
            
            # Update visit data
            update_vals = {}
            if data.get('notes'):
                update_vals['notes'] = data['notes']
            if data.get('customer_feedback'):
                update_vals['customer_feedback'] = data['customer_feedback']
            if data.get('visit_result'):
                update_vals['visit_result'] = data['visit_result']
            if data.get('next_visit_date'):
                update_vals['next_visit_date'] = data['next_visit_date']
            if data.get('follow_up_required'):
                update_vals['follow_up_required'] = data['follow_up_required']
            if data.get('follow_up_notes'):
                update_vals['follow_up_notes'] = data['follow_up_notes']
            
            if update_vals:
                visit.write(update_vals)
            
            visit.action_complete_visit()
            
            return self._json_response(data={
                'message': 'Visit completed successfully',
                'visit_id': visit.id,
                'state': visit.state
            })
            
        except Exception as e:
            _logger.error('Error completing visit: %s', str(e))
            return self._json_response(error=str(e), status=500)
    
    @http.route('/api/mobile/van/order/create', type='http', auth='user', methods=['POST'], csrf=False)
    def create_order(self, **kwargs):
        """Create sales order from mobile app"""
        try:
            user = self._authenticate_user()
            if not user:
                return self._json_response(error='Authentication required', status=401)
            
            data = json.loads(request.httprequest.data.decode('utf-8'))
            
            # Validate required fields
            customer_id = data.get('customer_id')
            visit_id = data.get('visit_id')
            order_lines = data.get('order_lines', [])
            
            if not customer_id or not order_lines:
                return self._json_response(error='Customer and order lines required', status=400)
            
            customer = request.env['van.customer'].browse(customer_id)
            if not customer.exists():
                return self._json_response(error='Customer not found', status=404)
            
            # Create sales order
            order_vals = {
                'partner_id': customer.partner_id.id,
                'van_sales_order': True,
                'van_salesperson_id': user.id,
                'order_line': []
            }
            
            if visit_id:
                order_vals['van_visit_id'] = visit_id
            
            if data.get('delivery_method'):
                order_vals['delivery_method'] = data['delivery_method']
            if data.get('payment_method'):
                order_vals['payment_method'] = data['payment_method']
            if data.get('latitude') and data.get('longitude'):
                order_vals['order_latitude'] = data['latitude']
                order_vals['order_longitude'] = data['longitude']
            
            # Add order lines
            for line_data in order_lines:
                product_id = line_data.get('product_id')
                quantity = line_data.get('quantity', 1)
                price_unit = line_data.get('price_unit')
                
                if not product_id:
                    continue
                
                product = request.env['product.product'].browse(product_id)
                if not product.exists():
                    continue
                
                line_vals = {
                    'product_id': product_id,
                    'product_uom_qty': quantity,
                    'name': product.name
                }
                
                if price_unit:
                    line_vals['price_unit'] = price_unit
                
                order_vals['order_line'].append((0, 0, line_vals))
            
            order = request.env['sale.order'].create(order_vals)
            
            return self._json_response(data={
                'message': 'Order created successfully',
                'order_id': order.id,
                'order_name': order.name,
                'amount_total': order.amount_total
            })
            
        except Exception as e:
            _logger.error('Error creating order: %s', str(e))
            return self._json_response(error=str(e), status=500)
