from odoo import models, fields, api, _


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # Van Sales Fields
    van_customer = fields.Boolean(
        string='Van Sales Customer',
        default=False,
        help='Mark this partner as a van sales customer'
    )
    van_latitude = fields.Float(
        string='Van Sales Latitude',
        digits=(10, 6),
        help='GPS latitude for van sales visits'
    )
    van_longitude = fields.Float(
        string='Van Sales Longitude',
        digits=(10, 6),
        help='GPS longitude for van sales visits'
    )
    van_territory_id = fields.Many2one(
        'van.territory',
        string='Van Sales Territory'
    )
    
    # Van Customer Record
    van_customer_id = fields.Many2one(
        'van.customer',
        string='Van Customer Record',
        help='Link to detailed van customer record'
    )
    
    # Visit Information
    last_van_visit_date = fields.Datetime(
        string='Last Van Visit',
        compute='_compute_van_visit_info',
        store=True
    )
    next_van_visit_date = fields.Date(
        string='Next Van Visit',
        compute='_compute_van_visit_info',
        store=True
    )
    total_van_visits = fields.Integer(
        string='Total Van Visits',
        compute='_compute_van_visit_info',
        store=True
    )
    
    # Van Sales Statistics
    van_sales_total = fields.Monetary(
        string='Van Sales Total',
        compute='_compute_van_sales_stats',
        currency_field='currency_id'
    )
    van_orders_count = fields.Integer(
        string='Van Orders Count',
        compute='_compute_van_sales_stats'
    )
    
    @api.depends('van_customer_id', 'van_customer_id.visit_ids')
    def _compute_van_visit_info(self):
        for partner in self:
            if partner.van_customer_id:
                visits = partner.van_customer_id.visit_ids
                partner.total_van_visits = len(visits)
                
                # Last visit
                completed_visits = visits.filtered(lambda v: v.state == 'completed')
                if completed_visits:
                    partner.last_van_visit_date = max(completed_visits.mapped('actual_departure_time'))
                else:
                    partner.last_van_visit_date = False
                
                # Next visit
                future_visits = visits.filtered(
                    lambda v: v.visit_date >= fields.Date.today() and v.state == 'planned'
                )
                if future_visits:
                    partner.next_van_visit_date = min(future_visits.mapped('visit_date'))
                else:
                    partner.next_van_visit_date = False
            else:
                partner.last_van_visit_date = False
                partner.next_van_visit_date = False
                partner.total_van_visits = 0
    
    @api.depends('sale_order_ids')
    def _compute_van_sales_stats(self):
        for partner in self:
            van_orders = partner.sale_order_ids.filtered(
                lambda o: o.van_sales_order and o.state in ['sale', 'done']
            )
            partner.van_orders_count = len(van_orders)
            partner.van_sales_total = sum(van_orders.mapped('amount_total'))
    
    def action_create_van_customer(self):
        """Create van customer record for this partner"""
        self.ensure_one()
        if self.van_customer_id:
            return self.action_view_van_customer()
        
        van_customer = self.env['van.customer'].create({
            'name': self.name,
            'partner_id': self.id,
            'latitude': self.van_latitude or 0.0,
            'longitude': self.van_longitude or 0.0,
        })
        
        self.van_customer_id = van_customer.id
        self.van_customer = True
        
        return self.action_view_van_customer()
    
    def action_view_van_customer(self):
        """View van customer record"""
        self.ensure_one()
        if not self.van_customer_id:
            return self.action_create_van_customer()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Van Customer'),
            'res_model': 'van.customer',
            'res_id': self.van_customer_id.id,
            'view_mode': 'form',
            'target': 'current'
        }
    
    def action_schedule_van_visit(self):
        """Schedule a van visit for this customer"""
        self.ensure_one()
        if not self.van_customer_id:
            self.action_create_van_customer()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Schedule Van Visit'),
            'res_model': 'van.visit.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_customer_id': self.van_customer_id.id,
                'default_partner_id': self.id
            }
        }
    
    def action_view_van_visits(self):
        """View all van visits for this customer"""
        self.ensure_one()
        if not self.van_customer_id:
            return {'type': 'ir.actions.act_window_close'}
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Van Visits'),
            'res_model': 'van.visit',
            'view_mode': 'tree,form',
            'domain': [('customer_id', '=', self.van_customer_id.id)],
            'context': {'default_customer_id': self.van_customer_id.id}
        }
    
    def action_view_van_orders(self):
        """View van sales orders for this customer"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Van Sales Orders'),
            'res_model': 'sale.order',
            'view_mode': 'tree,form',
            'domain': [
                ('partner_id', '=', self.id),
                ('van_sales_order', '=', True)
            ],
            'context': {
                'default_partner_id': self.id,
                'default_van_sales_order': True
            }
        }
    
    @api.model
    def search_van_customers_in_radius(self, center_lat, center_lng, radius_km):
        """Search for van customers within a radius"""
        # This is a simplified search - in production you'd use PostGIS or similar
        van_customers = self.search([
            ('van_customer', '=', True),
            ('van_latitude', '!=', 0),
            ('van_longitude', '!=', 0)
        ])
        
        result = []
        for customer in van_customers:
            distance = self._calculate_distance(
                center_lat, center_lng,
                customer.van_latitude, customer.van_longitude
            )
            if distance <= radius_km:
                result.append({
                    'partner': customer,
                    'distance': distance
                })
        
        return sorted(result, key=lambda x: x['distance'])
    
    def _calculate_distance(self, lat1, lng1, lat2, lng2):
        """Calculate distance between two points using Haversine formula"""
        from math import radians, sin, cos, sqrt, atan2
        
        # Convert to radians
        lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        # Earth's radius in kilometers
        r = 6371
        
        return r * c
