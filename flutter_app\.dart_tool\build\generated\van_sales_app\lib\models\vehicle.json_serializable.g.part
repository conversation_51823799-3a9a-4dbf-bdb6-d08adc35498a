// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Vehicle _$VehicleFromJson(Map<String, dynamic> json) => Vehicle(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      licensePlate: json['licensePlate'] as String,
      driverId: (json['driverId'] as num?)?.toInt(),
      driverName: json['driverName'] as String?,
      state: $enumDecodeNullable(_$VehicleStateEnumMap, json['state']) ??
          VehicleState.available,
      currentLatitude: (json['currentLatitude'] as num?)?.toDouble(),
      currentLongitude: (json['currentLongitude'] as num?)?.toDouble(),
      lastLocationUpdate: json['lastLocationUpdate'] == null
          ? null
          : DateTime.parse(json['lastLocationUpdate'] as String),
      maxWeightCapacity: (json['maxWeightCapacity'] as num?)?.toDouble(),
      maxVolumeCapacity: (json['maxVolumeCapacity'] as num?)?.toDouble(),
      fuelType: json['fuelType'] as String?,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$VehicleToJson(Vehicle instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'licensePlate': instance.licensePlate,
      'driverId': instance.driverId,
      'driverName': instance.driverName,
      'state': _$VehicleStateEnumMap[instance.state]!,
      'currentLatitude': instance.currentLatitude,
      'currentLongitude': instance.currentLongitude,
      'lastLocationUpdate': instance.lastLocationUpdate?.toIso8601String(),
      'maxWeightCapacity': instance.maxWeightCapacity,
      'maxVolumeCapacity': instance.maxVolumeCapacity,
      'fuelType': instance.fuelType,
      'isActive': instance.isActive,
    };

const _$VehicleStateEnumMap = {
  VehicleState.available: 'available',
  VehicleState.onRoute: 'onRoute',
  VehicleState.maintenance: 'maintenance',
  VehicleState.inactive: 'inactive',
};
