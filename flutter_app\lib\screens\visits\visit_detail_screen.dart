import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class VisitDetailScreen extends ConsumerWidget {
  final int visitId;
  
  const VisitDetailScreen({super.key, required this.visitId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: Text('Visit $visitId')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.assignment, size: 64, color: AppTheme.visitColor),
            const SizedBox(height: 16),
            Text('Visit Detail $visitId', style: AppTheme.headingMedium),
            const SizedBox(height: 8),
            const Text('Visit details coming soon', style: AppTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
