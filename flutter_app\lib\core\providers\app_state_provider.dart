import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_service.dart';

// App state
class AppState {
  final ThemeMode themeMode;
  final double textScale;
  final bool isOfflineMode;
  final bool isLocationEnabled;
  final bool areNotificationsEnabled;
  final DateTime? lastSyncTime;
  final bool isSyncing;
  final String? syncError;

  const AppState({
    this.themeMode = ThemeMode.system,
    this.textScale = 1.0,
    this.isOfflineMode = false,
    this.isLocationEnabled = true,
    this.areNotificationsEnabled = true,
    this.lastSyncTime,
    this.isSyncing = false,
    this.syncError,
  });

  AppState copyWith({
    ThemeMode? themeMode,
    double? textScale,
    bool? isOfflineMode,
    bool? isLocationEnabled,
    bool? areNotificationsEnabled,
    DateTime? lastSyncTime,
    bool? isSyncing,
    String? syncError,
  }) {
    return AppState(
      themeMode: themeMode ?? this.themeMode,
      textScale: textScale ?? this.textScale,
      isOfflineMode: isOfflineMode ?? this.isOfflineMode,
      isLocationEnabled: isLocationEnabled ?? this.isLocationEnabled,
      areNotificationsEnabled: areNotificationsEnabled ?? this.areNotificationsEnabled,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      isSyncing: isSyncing ?? this.isSyncing,
      syncError: syncError,
    );
  }
}

// App state notifier
class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier() : super(const AppState()) {
    _loadSettings();
  }

  void _loadSettings() {
    final themeIndex = StorageService.getInt('theme_mode') ?? 0;
    final textScale = StorageService.getDouble('text_scale') ?? 1.0;
    final isOfflineMode = StorageService.getBool('offline_mode') ?? false;
    final isLocationEnabled = StorageService.getBool('location_enabled') ?? true;
    final areNotificationsEnabled = StorageService.getBool('notifications_enabled') ?? true;
    final lastSyncTime = StorageService.getLastSyncTime();

    state = state.copyWith(
      themeMode: ThemeMode.values[themeIndex],
      textScale: textScale,
      isOfflineMode: isOfflineMode,
      isLocationEnabled: isLocationEnabled,
      areNotificationsEnabled: areNotificationsEnabled,
      lastSyncTime: lastSyncTime,
    );
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = state.copyWith(themeMode: themeMode);
    await StorageService.setInt('theme_mode', themeMode.index);
  }

  Future<void> setTextScale(double scale) async {
    state = state.copyWith(textScale: scale);
    await StorageService.setDouble('text_scale', scale);
  }

  Future<void> setOfflineMode(bool enabled) async {
    state = state.copyWith(isOfflineMode: enabled);
    await StorageService.setBool('offline_mode', enabled);
  }

  Future<void> setLocationEnabled(bool enabled) async {
    state = state.copyWith(isLocationEnabled: enabled);
    await StorageService.setBool('location_enabled', enabled);
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    state = state.copyWith(areNotificationsEnabled: enabled);
    await StorageService.setBool('notifications_enabled', enabled);
  }

  void setSyncing(bool syncing) {
    state = state.copyWith(isSyncing: syncing, syncError: null);
  }

  void setSyncComplete(DateTime syncTime) {
    state = state.copyWith(
      isSyncing: false,
      lastSyncTime: syncTime,
      syncError: null,
    );
    StorageService.setLastSyncTime(syncTime);
  }

  void setSyncError(String error) {
    state = state.copyWith(
      isSyncing: false,
      syncError: error,
    );
  }

  void clearSyncError() {
    state = state.copyWith(syncError: null);
  }
}

// Provider
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});

// Convenience providers
final themeModeProvider = Provider<ThemeMode>((ref) {
  return ref.watch(appStateProvider).themeMode;
});

final textScaleProvider = Provider<double>((ref) {
  return ref.watch(appStateProvider).textScale;
});

final isOfflineModeProvider = Provider<bool>((ref) {
  return ref.watch(appStateProvider).isOfflineMode;
});

final isLocationEnabledProvider = Provider<bool>((ref) {
  return ref.watch(appStateProvider).isLocationEnabled;
});

final areNotificationsEnabledProvider = Provider<bool>((ref) {
  return ref.watch(appStateProvider).areNotificationsEnabled;
});

final lastSyncTimeProvider = Provider<DateTime?>((ref) {
  return ref.watch(appStateProvider).lastSyncTime;
});

final isSyncingProvider = Provider<bool>((ref) {
  return ref.watch(appStateProvider).isSyncing;
});

final syncErrorProvider = Provider<String?>((ref) {
  return ref.watch(appStateProvider).syncError;
});
