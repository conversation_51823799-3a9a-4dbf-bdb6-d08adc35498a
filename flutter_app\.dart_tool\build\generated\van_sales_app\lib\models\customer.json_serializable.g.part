// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Customer _$CustomerFromJson(Map<String, dynamic> json) => Customer(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String?,
      territoryId: (json['territoryId'] as num?)?.toInt(),
      territoryName: json['territoryName'] as String?,
      customerType:
          $enumDecodeNullable(_$CustomerTypeEnumMap, json['customerType']) ??
              CustomerType.retail,
      priority:
          $enumDecodeNullable(_$CustomerPriorityEnumMap, json['priority']) ??
              CustomerPriority.medium,
      preferredVisitTime: $enumDecodeNullable(
              _$PreferredVisitTimeEnumMap, json['preferredVisitTime']) ??
          PreferredVisitTime.anytime,
      visitFrequency: $enumDecodeNullable(
              _$VisitFrequencyEnumMap, json['visitFrequency']) ??
          VisitFrequency.weekly,
      lastVisitDate: json['lastVisitDate'] == null
          ? null
          : DateTime.parse(json['lastVisitDate'] as String),
      totalVisits: (json['totalVisits'] as num?)?.toInt() ?? 0,
      totalSales: (json['totalSales'] as num?)?.toDouble() ?? 0.0,
      contactPerson: json['contactPerson'] as String?,
      contactPhone: json['contactPhone'] as String?,
      deliveryInstructions: json['deliveryInstructions'] as String?,
      accessRequirements: json['accessRequirements'] as String?,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$CustomerToJson(Customer instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'territoryId': instance.territoryId,
      'territoryName': instance.territoryName,
      'customerType': _$CustomerTypeEnumMap[instance.customerType]!,
      'priority': _$CustomerPriorityEnumMap[instance.priority]!,
      'preferredVisitTime':
          _$PreferredVisitTimeEnumMap[instance.preferredVisitTime]!,
      'visitFrequency': _$VisitFrequencyEnumMap[instance.visitFrequency]!,
      'lastVisitDate': instance.lastVisitDate?.toIso8601String(),
      'totalVisits': instance.totalVisits,
      'totalSales': instance.totalSales,
      'contactPerson': instance.contactPerson,
      'contactPhone': instance.contactPhone,
      'deliveryInstructions': instance.deliveryInstructions,
      'accessRequirements': instance.accessRequirements,
      'isActive': instance.isActive,
    };

const _$CustomerTypeEnumMap = {
  CustomerType.retail: 'retail',
  CustomerType.wholesale: 'wholesale',
  CustomerType.distributor: 'distributor',
  CustomerType.restaurant: 'restaurant',
  CustomerType.supermarket: 'supermarket',
};

const _$CustomerPriorityEnumMap = {
  CustomerPriority.low: 'low',
  CustomerPriority.medium: 'medium',
  CustomerPriority.high: 'high',
  CustomerPriority.critical: 'critical',
};

const _$PreferredVisitTimeEnumMap = {
  PreferredVisitTime.morning: 'morning',
  PreferredVisitTime.afternoon: 'afternoon',
  PreferredVisitTime.evening: 'evening',
  PreferredVisitTime.anytime: 'anytime',
};

const _$VisitFrequencyEnumMap = {
  VisitFrequency.daily: 'daily',
  VisitFrequency.weekly: 'weekly',
  VisitFrequency.biweekly: 'biweekly',
  VisitFrequency.monthly: 'monthly',
  VisitFrequency.onDemand: 'onDemand',
};
