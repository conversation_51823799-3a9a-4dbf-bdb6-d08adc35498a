import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';
import '../../core/providers/app_state_provider.dart';

class SyncStatusWidget extends ConsumerWidget {
  const SyncStatusWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isSyncing = ref.watch(isSyncingProvider);
    final lastSyncTime = ref.watch(lastSyncTimeProvider);
    final syncError = ref.watch(syncErrorProvider);

    return IconButton(
      icon: _buildSyncIcon(isSyncing, syncError),
      onPressed: () => _showSyncStatus(context, isSyncing, lastSyncTime, syncError),
      tooltip: _getSyncTooltip(isSyncing, lastSyncTime, syncError),
    );
  }

  Widget _buildSyncIcon(bool isSyncing, String? syncError) {
    if (isSyncing) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (syncError != null) {
      return const Icon(
        Icons.sync_problem,
        color: AppTheme.errorColor,
      );
    }

    return const Icon(
      Icons.sync,
      color: AppTheme.successColor,
    );
  }

  String _getSyncTooltip(bool isSyncing, DateTime? lastSyncTime, String? syncError) {
    if (isSyncing) {
      return 'Syncing data...';
    }

    if (syncError != null) {
      return 'Sync failed';
    }

    if (lastSyncTime != null) {
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime);
      
      if (difference.inMinutes < 1) {
        return 'Synced just now';
      } else if (difference.inMinutes < 60) {
        return 'Synced ${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return 'Synced ${difference.inHours}h ago';
      } else {
        return 'Synced ${difference.inDays}d ago';
      }
    }

    return 'Tap to sync';
  }

  void _showSyncStatus(BuildContext context, bool isSyncing, DateTime? lastSyncTime, String? syncError) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _SyncStatusBottomSheet(
        isSyncing: isSyncing,
        lastSyncTime: lastSyncTime,
        syncError: syncError,
      ),
    );
  }
}

class _SyncStatusBottomSheet extends ConsumerWidget {
  final bool isSyncing;
  final DateTime? lastSyncTime;
  final String? syncError;

  const _SyncStatusBottomSheet({
    required this.isSyncing,
    required this.lastSyncTime,
    required this.syncError,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildStatusIcon(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusTitle(),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getStatusSubtitle(),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          if (syncError != null) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.errorColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.errorColor.withOpacity(0.3),
                ),
              ),
              child: Text(
                syncError!,
                style: TextStyle(
                  color: AppTheme.errorColor,
                  fontSize: 12,
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 24),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: isSyncing ? null : () {
                    Navigator.of(context).pop();
                    _triggerSync(ref);
                  },
                  child: Text(isSyncing ? 'Syncing...' : 'Sync Now'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIcon() {
    if (isSyncing) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppTheme.infoColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(24),
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.infoColor),
            ),
          ),
        ),
      );
    }

    if (syncError != null) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppTheme.errorColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(24),
        ),
        child: const Icon(
          Icons.sync_problem,
          color: AppTheme.errorColor,
          size: 24,
        ),
      );
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: AppTheme.successColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(24),
      ),
      child: const Icon(
        Icons.check_circle,
        color: AppTheme.successColor,
        size: 24,
      ),
    );
  }

  String _getStatusTitle() {
    if (isSyncing) return 'Syncing Data';
    if (syncError != null) return 'Sync Failed';
    return 'Data Synchronized';
  }

  String _getStatusSubtitle() {
    if (isSyncing) return 'Please wait while we sync your data...';
    if (syncError != null) return 'There was an error syncing your data';
    
    if (lastSyncTime != null) {
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime!);
      
      if (difference.inMinutes < 1) {
        return 'Last synced just now';
      } else if (difference.inMinutes < 60) {
        return 'Last synced ${difference.inMinutes} minutes ago';
      } else if (difference.inHours < 24) {
        return 'Last synced ${difference.inHours} hours ago';
      } else {
        return 'Last synced ${difference.inDays} days ago';
      }
    }

    return 'Tap sync now to update your data';
  }

  void _triggerSync(WidgetRef ref) {
    // TODO: Implement sync functionality
    ref.read(appStateProvider.notifier).setSyncing(true);
    
    // Simulate sync process
    Future.delayed(const Duration(seconds: 3), () {
      ref.read(appStateProvider.notifier).setSyncComplete(DateTime.now());
    });
  }
}
