from odoo import models, fields, api, _


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Van Sales Fields
    van_sales_order = fields.Boolean(
        string='Van Sales Order',
        default=False,
        help='Mark this order as a van sales order'
    )
    van_visit_id = fields.Many2one(
        'van.visit',
        string='Van Visit',
        help='Visit during which this order was created'
    )
    van_vehicle_id = fields.Many2one(
        'van.vehicle',
        string='Van Vehicle',
        related='van_visit_id.vehicle_id',
        store=True,
        readonly=True
    )
    van_route_id = fields.Many2one(
        'van.route',
        string='Van Route',
        related='van_visit_id.route_id',
        store=True,
        readonly=True
    )
    
    # Location Information
    order_latitude = fields.Float(
        string='Order Latitude',
        digits=(10, 6),
        help='GPS coordinates where order was taken'
    )
    order_longitude = fields.Float(
        string='Order Longitude',
        digits=(10, 6),
        help='GPS coordinates where order was taken'
    )
    
    # Van Sales Specific Fields
    delivery_method = fields.Selection([
        ('immediate', 'Immediate Delivery'),
        ('scheduled', 'Scheduled Delivery'),
        ('pickup', 'Customer Pickup'),
        ('next_visit', 'Next Visit Delivery')
    ], string='Delivery Method', default='immediate')
    
    van_delivery_date = fields.Datetime(
        string='Van Delivery Date'
    )
    van_delivery_notes = fields.Text(
        string='Van Delivery Notes'
    )
    
    # Payment Information
    payment_method = fields.Selection([
        ('cash', 'Cash'),
        ('card', 'Card'),
        ('credit', 'Credit'),
        ('bank_transfer', 'Bank Transfer'),
        ('mobile_payment', 'Mobile Payment')
    ], string='Payment Method')
    
    cash_received = fields.Monetary(
        string='Cash Received',
        currency_field='currency_id'
    )
    change_given = fields.Monetary(
        string='Change Given',
        currency_field='currency_id'
    )
    
    # Order Status for Van Sales
    van_order_state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('loaded', 'Loaded on Van'),
        ('delivered', 'Delivered'),
        ('paid', 'Paid'),
        ('returned', 'Returned')
    ], string='Van Order Status', default='draft', tracking=True)
    
    # Signature and Confirmation
    customer_signature = fields.Binary(
        string='Customer Signature'
    )
    delivery_confirmation = fields.Boolean(
        string='Delivery Confirmed'
    )
    
    # Van Sales Team
    van_salesperson_id = fields.Many2one(
        'res.users',
        string='Van Salesperson',
        default=lambda self: self.env.user
    )
    
    @api.model
    def create(self, vals):
        """Override create to handle van sales orders"""
        order = super().create(vals)
        
        # Auto-set van sales order if created from visit
        if vals.get('van_visit_id'):
            order.van_sales_order = True
        
        return order
    
    def action_confirm_van_order(self):
        """Confirm van sales order"""
        self.ensure_one()
        if not self.van_sales_order:
            return super().action_confirm()
        
        # Confirm the sale order
        self.action_confirm()
        
        # Update van order state
        self.van_order_state = 'confirmed'
        
        # Create delivery if immediate delivery
        if self.delivery_method == 'immediate':
            self._create_van_delivery()
        
        self.message_post(
            body=_('Van sales order confirmed'),
            message_type='notification'
        )
    
    def action_load_on_van(self):
        """Mark order as loaded on van"""
        self.ensure_one()
        self.van_order_state = 'loaded'
        
        self.message_post(
            body=_('Order loaded on van'),
            message_type='notification'
        )
    
    def action_deliver_order(self):
        """Mark order as delivered"""
        self.ensure_one()
        if self.van_order_state != 'loaded':
            raise UserError(_('Order must be loaded on van before delivery.'))
        
        self.write({
            'van_order_state': 'delivered',
            'van_delivery_date': fields.Datetime.now(),
            'delivery_confirmation': True
        })
        
        # Auto-create invoice if configured
        if self.company_id.van_auto_invoice:
            self._create_invoices()
        
        self.message_post(
            body=_('Order delivered at %s') % fields.Datetime.now(),
            message_type='notification'
        )
    
    def action_receive_payment(self):
        """Process payment for van sales order"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Receive Payment'),
            'res_model': 'van.payment.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_order_id': self.id,
                'default_amount': self.amount_total,
                'default_payment_method': self.payment_method or 'cash'
            }
        }
    
    def action_return_order(self):
        """Process order return"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Return Order'),
            'res_model': 'van.return.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_order_id': self.id
            }
        }
    
    def _create_van_delivery(self):
        """Create delivery for van sales order"""
        if not self.picking_ids:
            # Create picking if not exists
            self._action_confirm()
        
        # Update picking for van delivery
        for picking in self.picking_ids:
            picking.write({
                'van_delivery': True,
                'van_vehicle_id': self.van_vehicle_id.id if self.van_vehicle_id else False,
                'scheduled_date': self.van_delivery_date or fields.Datetime.now()
            })
    
    def update_order_location(self, latitude, longitude):
        """Update order location from mobile app"""
        self.ensure_one()
        self.write({
            'order_latitude': latitude,
            'order_longitude': longitude
        })
    
    @api.depends('van_sales_order', 'van_visit_id')
    def _compute_access_url(self):
        """Override to provide van sales specific URLs"""
        super()._compute_access_url()
        for order in self:
            if order.van_sales_order:
                order.access_url = '/van/order/%s' % order.id
    
    def _get_van_order_summary(self):
        """Get order summary for mobile app"""
        self.ensure_one()
        return {
            'id': self.id,
            'name': self.name,
            'partner_name': self.partner_id.name,
            'amount_total': self.amount_total,
            'currency': self.currency_id.name,
            'state': self.state,
            'van_order_state': self.van_order_state,
            'delivery_method': self.delivery_method,
            'payment_method': self.payment_method,
            'order_lines': [{
                'product': line.product_id.name,
                'quantity': line.product_uom_qty,
                'price_unit': line.price_unit,
                'price_subtotal': line.price_subtotal
            } for line in self.order_line]
        }


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Van Sales Specific Fields
    van_stock_available = fields.Float(
        string='Van Stock Available',
        help='Available stock on the van for this product'
    )
    van_delivered_qty = fields.Float(
        string='Van Delivered Quantity',
        help='Quantity delivered from van'
    )
    van_returned_qty = fields.Float(
        string='Van Returned Quantity',
        help='Quantity returned to van'
    )
    
    @api.onchange('product_id')
    def _onchange_product_van_stock(self):
        """Check van stock availability when product changes"""
        if self.order_id.van_sales_order and self.order_id.van_vehicle_id:
            # Get available stock on van
            stock_quant = self.env['stock.quant'].search([
                ('product_id', '=', self.product_id.id),
                ('location_id.van_vehicle_id', '=', self.order_id.van_vehicle_id.id)
            ])
            self.van_stock_available = sum(stock_quant.mapped('quantity'))
    
    @api.constrains('product_uom_qty', 'van_stock_available')
    def _check_van_stock(self):
        """Validate that ordered quantity doesn't exceed van stock"""
        for line in self:
            if (line.order_id.van_sales_order and 
                line.van_stock_available > 0 and 
                line.product_uom_qty > line.van_stock_available):
                raise ValidationError(
                    _('Ordered quantity (%s) exceeds available van stock (%s) for product %s') % 
                    (line.product_uom_qty, line.van_stock_available, line.product_id.name)
                )
