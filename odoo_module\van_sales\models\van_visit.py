from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class VanVisit(models.Model):
    _name = 'van.visit'
    _description = 'Van Customer Visit'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'visit_date desc, sequence, id'

    name = fields.Char(
        string='Visit Reference',
        required=True,
        copy=False,
        default=lambda self: _('New Visit')
    )
    
    # Customer and Route Information
    customer_id = fields.Many2one(
        'van.customer',
        string='Customer',
        required=True,
        tracking=True
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='Partner',
        related='customer_id.partner_id',
        store=True,
        readonly=True
    )
    route_id = fields.Many2one(
        'van.route',
        string='Route',
        tracking=True
    )
    vehicle_id = fields.Many2one(
        'van.vehicle',
        string='Vehicle',
        related='route_id.vehicle_id',
        store=True,
        readonly=True
    )
    
    # Visit Planning
    visit_date = fields.Date(
        string='Visit Date',
        required=True,
        default=fields.Date.today,
        tracking=True
    )
    planned_time = fields.Datetime(
        string='Planned Time',
        tracking=True
    )
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Visit sequence in the route'
    )
    
    # Visit Execution
    actual_arrival_time = fields.Datetime(
        string='Actual Arrival Time'
    )
    actual_departure_time = fields.Datetime(
        string='Actual Departure Time'
    )
    duration = fields.Float(
        string='Duration (minutes)',
        compute='_compute_duration',
        store=True
    )
    
    # Visit Status
    state = fields.Selection([
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show')
    ], string='Status', default='planned', tracking=True)
    
    # Visit Type and Purpose
    visit_type = fields.Selection([
        ('sales', 'Sales Visit'),
        ('delivery', 'Delivery'),
        ('collection', 'Collection'),
        ('service', 'Service'),
        ('survey', 'Survey'),
        ('maintenance', 'Maintenance')
    ], string='Visit Type', default='sales', required=True)
    
    purpose = fields.Text(
        string='Visit Purpose'
    )
    
    # Location Information
    customer_latitude = fields.Float(
        string='Customer Latitude',
        related='customer_id.latitude',
        readonly=True
    )
    customer_longitude = fields.Float(
        string='Customer Longitude',
        related='customer_id.longitude',
        readonly=True
    )
    actual_latitude = fields.Float(
        string='Actual Latitude',
        digits=(10, 6),
        help='GPS coordinates where visit actually took place'
    )
    actual_longitude = fields.Float(
        string='Actual Longitude',
        digits=(10, 6),
        help='GPS coordinates where visit actually took place'
    )
    
    # Visit Results
    visit_result = fields.Selection([
        ('successful', 'Successful'),
        ('partial', 'Partial Success'),
        ('unsuccessful', 'Unsuccessful'),
        ('rescheduled', 'Rescheduled')
    ], string='Visit Result')
    
    notes = fields.Text(
        string='Visit Notes'
    )
    customer_feedback = fields.Text(
        string='Customer Feedback'
    )
    
    # Sales Information
    sales_order_ids = fields.One2many(
        'sale.order',
        'van_visit_id',
        string='Sales Orders'
    )
    total_sales = fields.Monetary(
        string='Total Sales',
        compute='_compute_sales_metrics',
        currency_field='currency_id'
    )
    order_count = fields.Integer(
        string='Order Count',
        compute='_compute_sales_metrics'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Territory Validation
    territory_id = fields.Many2one(
        'van.territory',
        string='Territory',
        related='customer_id.territory_id',
        store=True,
        readonly=True
    )
    is_in_territory = fields.Boolean(
        string='In Authorized Territory',
        compute='_compute_territory_validation',
        store=True
    )
    
    # Photos and Attachments
    photo_ids = fields.One2many(
        'ir.attachment',
        'res_id',
        string='Photos',
        domain=[('res_model', '=', 'van.visit'), ('mimetype', 'like', 'image%')]
    )
    
    # Next Visit Planning
    next_visit_date = fields.Date(
        string='Next Planned Visit'
    )
    follow_up_required = fields.Boolean(
        string='Follow-up Required'
    )
    follow_up_notes = fields.Text(
        string='Follow-up Notes'
    )
    
    @api.depends('actual_arrival_time', 'actual_departure_time')
    def _compute_duration(self):
        for visit in self:
            if visit.actual_arrival_time and visit.actual_departure_time:
                delta = visit.actual_departure_time - visit.actual_arrival_time
                visit.duration = delta.total_seconds() / 60  # Convert to minutes
            else:
                visit.duration = 0.0
    
    @api.depends('sales_order_ids')
    def _compute_sales_metrics(self):
        for visit in self:
            confirmed_orders = visit.sales_order_ids.filtered(
                lambda o: o.state in ['sale', 'done']
            )
            visit.order_count = len(confirmed_orders)
            visit.total_sales = sum(confirmed_orders.mapped('amount_total'))
    
    @api.depends('actual_latitude', 'actual_longitude', 'territory_id')
    def _compute_territory_validation(self):
        for visit in self:
            if visit.actual_latitude and visit.actual_longitude and visit.territory_id:
                visit.is_in_territory = visit.territory_id.is_point_in_territory(
                    visit.actual_latitude, visit.actual_longitude
                )
            else:
                visit.is_in_territory = True  # Default to True if no validation possible
    
    @api.model
    def create(self, vals):
        """Generate visit reference if not provided"""
        if vals.get('name', _('New Visit')) == _('New Visit'):
            vals['name'] = self.env['ir.sequence'].next_by_code('van.visit') or _('New Visit')
        return super().create(vals)
    
    def action_start_visit(self):
        """Start the visit"""
        self.ensure_one()
        if self.state != 'planned':
            raise UserError(_('Only planned visits can be started.'))
        
        self.write({
            'state': 'in_progress',
            'actual_arrival_time': fields.Datetime.now()
        })
        
        self.message_post(
            body=_('Visit started at %s') % fields.Datetime.now(),
            message_type='notification'
        )
    
    def action_complete_visit(self):
        """Complete the visit"""
        self.ensure_one()
        if self.state != 'in_progress':
            raise UserError(_('Only visits in progress can be completed.'))
        
        self.write({
            'state': 'completed',
            'actual_departure_time': fields.Datetime.now()
        })
        
        # Auto-schedule next visit based on customer frequency
        self._schedule_next_visit()
        
        self.message_post(
            body=_('Visit completed at %s') % fields.Datetime.now(),
            message_type='notification'
        )
    
    def action_cancel_visit(self):
        """Cancel the visit"""
        self.ensure_one()
        if self.state == 'completed':
            raise UserError(_('Cannot cancel a completed visit.'))
        
        self.state = 'cancelled'
        
        self.message_post(
            body=_('Visit cancelled'),
            message_type='notification'
        )
    
    def action_mark_no_show(self):
        """Mark visit as no show"""
        self.ensure_one()
        self.state = 'no_show'
        
        self.message_post(
            body=_('Visit marked as no show'),
            message_type='notification'
        )
    
    def action_create_sales_order(self):
        """Create a new sales order for this visit"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Create Sales Order'),
            'res_model': 'sale.order',
            'view_mode': 'form',
            'target': 'current',
            'context': {
                'default_partner_id': self.partner_id.id,
                'default_van_visit_id': self.id,
                'default_van_sales_order': True
            }
        }
    
    def action_update_location(self, latitude, longitude):
        """Update visit location from mobile app"""
        self.ensure_one()
        self.write({
            'actual_latitude': latitude,
            'actual_longitude': longitude
        })
        
        # Validate territory if configured
        if not self.is_in_territory and self.territory_id:
            self.message_post(
                body=_('Warning: Visit location is outside authorized territory!'),
                message_type='notification',
                subtype_xmlid='mail.mt_comment'
            )
    
    def _schedule_next_visit(self):
        """Auto-schedule next visit based on customer frequency"""
        if not self.customer_id.visit_frequency or self.customer_id.visit_frequency == 'on_demand':
            return
        
        frequency_days = {
            'daily': 1,
            'weekly': 7,
            'biweekly': 14,
            'monthly': 30
        }
        
        days = frequency_days.get(self.customer_id.visit_frequency, 7)
        next_date = fields.Date.today() + timedelta(days=days)
        
        # Check if next visit already exists
        existing_visit = self.search([
            ('customer_id', '=', self.customer_id.id),
            ('visit_date', '=', next_date),
            ('state', '!=', 'cancelled')
        ])
        
        if not existing_visit:
            self.create({
                'customer_id': self.customer_id.id,
                'visit_date': next_date,
                'visit_type': self.visit_type,
                'purpose': 'Scheduled follow-up visit'
            })
    
    @api.constrains('actual_latitude', 'actual_longitude')
    def _check_coordinates(self):
        for visit in self:
            if visit.actual_latitude and not (-90 <= visit.actual_latitude <= 90):
                raise ValidationError(_('Latitude must be between -90 and 90 degrees.'))
            if visit.actual_longitude and not (-180 <= visit.actual_longitude <= 180):
                raise ValidationError(_('Longitude must be between -180 and 180 degrees.'))
