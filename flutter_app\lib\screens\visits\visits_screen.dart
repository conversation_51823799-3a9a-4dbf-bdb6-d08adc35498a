import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class VisitsScreen extends ConsumerWidget {
  const VisitsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visits'),
        actions: [
          IconButton(icon: const Icon(Icons.filter_list), onPressed: () {}),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment, size: 64, color: AppTheme.visitColor),
            Sized<PERSON><PERSON>(height: 16),
            Text('Visits Screen', style: AppTheme.headingMedium),
            SizedBox(height: 8),
            Text('Visit management coming soon', style: AppTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
