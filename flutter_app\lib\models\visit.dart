import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'visit.g.dart';

@HiveType(typeId: 11)
@JsonSerializable()
class Visit {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final int sequence;

  @HiveField(3)
  final int customerId;

  @HiveField(4)
  final String customerName;

  @HiveField(5)
  final double? customerLatitude;

  @HiveField(6)
  final double? customerLongitude;

  @HiveField(7)
  final DateTime visitDate;

  @HiveField(8)
  final DateTime? plannedTime;

  @HiveField(9)
  final VisitState state;

  @HiveField(10)
  final VisitType visitType;

  @HiveField(11)
  final String? purpose;

  @HiveField(12)
  final String? notes;

  @HiveField(13)
  final DateTime? actualArrivalTime;

  @HiveField(14)
  final DateTime? actualDepartureTime;

  @HiveField(15)
  final double? actualLatitude;

  @HiveField(16)
  final double? actualLongitude;

  @HiveField(17)
  final VisitResult? visitResult;

  @HiveField(18)
  final String? customerFeedback;

  @HiveField(19)
  final DateTime? nextVisitDate;

  @HiveField(20)
  final bool followUpRequired;

  @HiveField(21)
  final String? followUpNotes;

  @HiveField(22)
  final double totalSales;

  @HiveField(23)
  final int orderCount;

  const Visit({
    required this.id,
    required this.name,
    this.sequence = 0,
    required this.customerId,
    required this.customerName,
    this.customerLatitude,
    this.customerLongitude,
    required this.visitDate,
    this.plannedTime,
    this.state = VisitState.planned,
    this.visitType = VisitType.sales,
    this.purpose,
    this.notes,
    this.actualArrivalTime,
    this.actualDepartureTime,
    this.actualLatitude,
    this.actualLongitude,
    this.visitResult,
    this.customerFeedback,
    this.nextVisitDate,
    this.followUpRequired = false,
    this.followUpNotes,
    this.totalSales = 0.0,
    this.orderCount = 0,
  });

  factory Visit.fromJson(Map<String, dynamic> json) => _$VisitFromJson(json);
  Map<String, dynamic> toJson() => _$VisitToJson(this);

  Visit copyWith({
    int? id,
    String? name,
    int? sequence,
    int? customerId,
    String? customerName,
    double? customerLatitude,
    double? customerLongitude,
    DateTime? visitDate,
    DateTime? plannedTime,
    VisitState? state,
    VisitType? visitType,
    String? purpose,
    String? notes,
    DateTime? actualArrivalTime,
    DateTime? actualDepartureTime,
    double? actualLatitude,
    double? actualLongitude,
    VisitResult? visitResult,
    String? customerFeedback,
    DateTime? nextVisitDate,
    bool? followUpRequired,
    String? followUpNotes,
    double? totalSales,
    int? orderCount,
  }) {
    return Visit(
      id: id ?? this.id,
      name: name ?? this.name,
      sequence: sequence ?? this.sequence,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerLatitude: customerLatitude ?? this.customerLatitude,
      customerLongitude: customerLongitude ?? this.customerLongitude,
      visitDate: visitDate ?? this.visitDate,
      plannedTime: plannedTime ?? this.plannedTime,
      state: state ?? this.state,
      visitType: visitType ?? this.visitType,
      purpose: purpose ?? this.purpose,
      notes: notes ?? this.notes,
      actualArrivalTime: actualArrivalTime ?? this.actualArrivalTime,
      actualDepartureTime: actualDepartureTime ?? this.actualDepartureTime,
      actualLatitude: actualLatitude ?? this.actualLatitude,
      actualLongitude: actualLongitude ?? this.actualLongitude,
      visitResult: visitResult ?? this.visitResult,
      customerFeedback: customerFeedback ?? this.customerFeedback,
      nextVisitDate: nextVisitDate ?? this.nextVisitDate,
      followUpRequired: followUpRequired ?? this.followUpRequired,
      followUpNotes: followUpNotes ?? this.followUpNotes,
      totalSales: totalSales ?? this.totalSales,
      orderCount: orderCount ?? this.orderCount,
    );
  }

  Duration? get duration {
    if (actualArrivalTime != null && actualDepartureTime != null) {
      return actualDepartureTime!.difference(actualArrivalTime!);
    }
    return null;
  }

  bool get hasLocation => customerLatitude != null && customerLongitude != null;
  bool get hasActualLocation => actualLatitude != null && actualLongitude != null;
  bool get isCompleted => state == VisitState.completed;
  bool get isInProgress => state == VisitState.inProgress;
  bool get canStart => state == VisitState.planned;
  bool get canComplete => state == VisitState.inProgress;

  String get stateDisplayName {
    switch (state) {
      case VisitState.planned:
        return 'Planned';
      case VisitState.inProgress:
        return 'In Progress';
      case VisitState.completed:
        return 'Completed';
      case VisitState.cancelled:
        return 'Cancelled';
      case VisitState.noShow:
        return 'No Show';
    }
  }

  String get visitTypeDisplayName {
    switch (visitType) {
      case VisitType.sales:
        return 'Sales Visit';
      case VisitType.delivery:
        return 'Delivery';
      case VisitType.collection:
        return 'Collection';
      case VisitType.service:
        return 'Service';
      case VisitType.survey:
        return 'Survey';
      case VisitType.maintenance:
        return 'Maintenance';
    }
  }

  String? get visitResultDisplayName {
    if (visitResult == null) return null;
    switch (visitResult!) {
      case VisitResult.successful:
        return 'Successful';
      case VisitResult.partial:
        return 'Partial Success';
      case VisitResult.unsuccessful:
        return 'Unsuccessful';
      case VisitResult.rescheduled:
        return 'Rescheduled';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Visit && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Visit{id: $id, name: $name, customerName: $customerName, state: $state}';
  }
}

@HiveType(typeId: 12)
enum VisitState {
  @HiveField(0)
  planned,
  @HiveField(1)
  inProgress,
  @HiveField(2)
  completed,
  @HiveField(3)
  cancelled,
  @HiveField(4)
  noShow,
}

@HiveType(typeId: 13)
enum VisitType {
  @HiveField(0)
  sales,
  @HiveField(1)
  delivery,
  @HiveField(2)
  collection,
  @HiveField(3)
  service,
  @HiveField(4)
  survey,
  @HiveField(5)
  maintenance,
}

@HiveType(typeId: 14)
enum VisitResult {
  @HiveField(0)
  successful,
  @HiveField(1)
  partial,
  @HiveField(2)
  unsuccessful,
  @HiveField(3)
  rescheduled,
}
