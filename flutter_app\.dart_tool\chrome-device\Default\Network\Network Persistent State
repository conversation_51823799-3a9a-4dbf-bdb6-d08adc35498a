{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147355555817", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACYAAABodHRwczovL2xlbnNmcm9udGVuZC1wYS5nb29nbGVhcGlzLmNvbQAA", false, 0], "server": "https://lensfrontend-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147360469769", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "server": "https://id.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147360242444", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 52225}, "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147360634814", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 48966}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147360964784", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 55688}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147361913744", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 51666}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147363154239", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 56450}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147360386435", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 51804}, "server": "https://www.googleadservices.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 49841}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 75572}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 61470}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 48421}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 82129}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL2Nocm9tZS1kZXZ0b29scy1mcm9udGVuZC5hcHBzcG90LmNvbQ==", false, 0], "network_stats": {"srtt": 62092}, "server": "https://chrome-devtools-frontend.appspot.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395147363301524", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 60208}, "server": "https://www.google.com"}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}