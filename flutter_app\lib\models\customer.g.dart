// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomerAdapter extends TypeAdapter<Customer> {
  @override
  final int typeId = 6;

  @override
  Customer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Customer(
      id: fields[0] as int,
      name: fields[1] as String,
      latitude: fields[2] as double,
      longitude: fields[3] as double,
      address: fields[4] as String?,
      territoryId: fields[5] as int?,
      territoryName: fields[6] as String?,
      customerType: fields[7] as CustomerType,
      priority: fields[8] as CustomerPriority,
      preferredVisitTime: fields[9] as PreferredVisitTime,
      visitFrequency: fields[10] as VisitFrequency,
      lastVisitDate: fields[11] as DateTime?,
      totalVisits: fields[12] as int,
      totalSales: fields[13] as double,
      contactPerson: fields[14] as String?,
      contactPhone: fields[15] as String?,
      deliveryInstructions: fields[16] as String?,
      accessRequirements: fields[17] as String?,
      isActive: fields[18] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Customer obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.latitude)
      ..writeByte(3)
      ..write(obj.longitude)
      ..writeByte(4)
      ..write(obj.address)
      ..writeByte(5)
      ..write(obj.territoryId)
      ..writeByte(6)
      ..write(obj.territoryName)
      ..writeByte(7)
      ..write(obj.customerType)
      ..writeByte(8)
      ..write(obj.priority)
      ..writeByte(9)
      ..write(obj.preferredVisitTime)
      ..writeByte(10)
      ..write(obj.visitFrequency)
      ..writeByte(11)
      ..write(obj.lastVisitDate)
      ..writeByte(12)
      ..write(obj.totalVisits)
      ..writeByte(13)
      ..write(obj.totalSales)
      ..writeByte(14)
      ..write(obj.contactPerson)
      ..writeByte(15)
      ..write(obj.contactPhone)
      ..writeByte(16)
      ..write(obj.deliveryInstructions)
      ..writeByte(17)
      ..write(obj.accessRequirements)
      ..writeByte(18)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerTypeAdapter extends TypeAdapter<CustomerType> {
  @override
  final int typeId = 7;

  @override
  CustomerType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CustomerType.retail;
      case 1:
        return CustomerType.wholesale;
      case 2:
        return CustomerType.distributor;
      case 3:
        return CustomerType.restaurant;
      case 4:
        return CustomerType.supermarket;
      default:
        return CustomerType.retail;
    }
  }

  @override
  void write(BinaryWriter writer, CustomerType obj) {
    switch (obj) {
      case CustomerType.retail:
        writer.writeByte(0);
        break;
      case CustomerType.wholesale:
        writer.writeByte(1);
        break;
      case CustomerType.distributor:
        writer.writeByte(2);
        break;
      case CustomerType.restaurant:
        writer.writeByte(3);
        break;
      case CustomerType.supermarket:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerPriorityAdapter extends TypeAdapter<CustomerPriority> {
  @override
  final int typeId = 8;

  @override
  CustomerPriority read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CustomerPriority.low;
      case 1:
        return CustomerPriority.medium;
      case 2:
        return CustomerPriority.high;
      case 3:
        return CustomerPriority.critical;
      default:
        return CustomerPriority.low;
    }
  }

  @override
  void write(BinaryWriter writer, CustomerPriority obj) {
    switch (obj) {
      case CustomerPriority.low:
        writer.writeByte(0);
        break;
      case CustomerPriority.medium:
        writer.writeByte(1);
        break;
      case CustomerPriority.high:
        writer.writeByte(2);
        break;
      case CustomerPriority.critical:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerPriorityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PreferredVisitTimeAdapter extends TypeAdapter<PreferredVisitTime> {
  @override
  final int typeId = 9;

  @override
  PreferredVisitTime read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PreferredVisitTime.morning;
      case 1:
        return PreferredVisitTime.afternoon;
      case 2:
        return PreferredVisitTime.evening;
      case 3:
        return PreferredVisitTime.anytime;
      default:
        return PreferredVisitTime.morning;
    }
  }

  @override
  void write(BinaryWriter writer, PreferredVisitTime obj) {
    switch (obj) {
      case PreferredVisitTime.morning:
        writer.writeByte(0);
        break;
      case PreferredVisitTime.afternoon:
        writer.writeByte(1);
        break;
      case PreferredVisitTime.evening:
        writer.writeByte(2);
        break;
      case PreferredVisitTime.anytime:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PreferredVisitTimeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VisitFrequencyAdapter extends TypeAdapter<VisitFrequency> {
  @override
  final int typeId = 10;

  @override
  VisitFrequency read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VisitFrequency.daily;
      case 1:
        return VisitFrequency.weekly;
      case 2:
        return VisitFrequency.biweekly;
      case 3:
        return VisitFrequency.monthly;
      case 4:
        return VisitFrequency.onDemand;
      default:
        return VisitFrequency.daily;
    }
  }

  @override
  void write(BinaryWriter writer, VisitFrequency obj) {
    switch (obj) {
      case VisitFrequency.daily:
        writer.writeByte(0);
        break;
      case VisitFrequency.weekly:
        writer.writeByte(1);
        break;
      case VisitFrequency.biweekly:
        writer.writeByte(2);
        break;
      case VisitFrequency.monthly:
        writer.writeByte(3);
        break;
      case VisitFrequency.onDemand:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisitFrequencyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Customer _$CustomerFromJson(Map<String, dynamic> json) => Customer(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String?,
      territoryId: (json['territoryId'] as num?)?.toInt(),
      territoryName: json['territoryName'] as String?,
      customerType:
          $enumDecodeNullable(_$CustomerTypeEnumMap, json['customerType']) ??
              CustomerType.retail,
      priority:
          $enumDecodeNullable(_$CustomerPriorityEnumMap, json['priority']) ??
              CustomerPriority.medium,
      preferredVisitTime: $enumDecodeNullable(
              _$PreferredVisitTimeEnumMap, json['preferredVisitTime']) ??
          PreferredVisitTime.anytime,
      visitFrequency: $enumDecodeNullable(
              _$VisitFrequencyEnumMap, json['visitFrequency']) ??
          VisitFrequency.weekly,
      lastVisitDate: json['lastVisitDate'] == null
          ? null
          : DateTime.parse(json['lastVisitDate'] as String),
      totalVisits: (json['totalVisits'] as num?)?.toInt() ?? 0,
      totalSales: (json['totalSales'] as num?)?.toDouble() ?? 0.0,
      contactPerson: json['contactPerson'] as String?,
      contactPhone: json['contactPhone'] as String?,
      deliveryInstructions: json['deliveryInstructions'] as String?,
      accessRequirements: json['accessRequirements'] as String?,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$CustomerToJson(Customer instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'territoryId': instance.territoryId,
      'territoryName': instance.territoryName,
      'customerType': _$CustomerTypeEnumMap[instance.customerType]!,
      'priority': _$CustomerPriorityEnumMap[instance.priority]!,
      'preferredVisitTime':
          _$PreferredVisitTimeEnumMap[instance.preferredVisitTime]!,
      'visitFrequency': _$VisitFrequencyEnumMap[instance.visitFrequency]!,
      'lastVisitDate': instance.lastVisitDate?.toIso8601String(),
      'totalVisits': instance.totalVisits,
      'totalSales': instance.totalSales,
      'contactPerson': instance.contactPerson,
      'contactPhone': instance.contactPhone,
      'deliveryInstructions': instance.deliveryInstructions,
      'accessRequirements': instance.accessRequirements,
      'isActive': instance.isActive,
    };

const _$CustomerTypeEnumMap = {
  CustomerType.retail: 'retail',
  CustomerType.wholesale: 'wholesale',
  CustomerType.distributor: 'distributor',
  CustomerType.restaurant: 'restaurant',
  CustomerType.supermarket: 'supermarket',
};

const _$CustomerPriorityEnumMap = {
  CustomerPriority.low: 'low',
  CustomerPriority.medium: 'medium',
  CustomerPriority.high: 'high',
  CustomerPriority.critical: 'critical',
};

const _$PreferredVisitTimeEnumMap = {
  PreferredVisitTime.morning: 'morning',
  PreferredVisitTime.afternoon: 'afternoon',
  PreferredVisitTime.evening: 'evening',
  PreferredVisitTime.anytime: 'anytime',
};

const _$VisitFrequencyEnumMap = {
  VisitFrequency.daily: 'daily',
  VisitFrequency.weekly: 'weekly',
  VisitFrequency.biweekly: 'biweekly',
  VisitFrequency.monthly: 'monthly',
  VisitFrequency.onDemand: 'onDemand',
};
