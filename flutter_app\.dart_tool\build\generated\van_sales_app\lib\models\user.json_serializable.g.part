// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      vehicleId: (json['vehicleId'] as num?)?.toInt(),
      vehicleName: json['vehicleName'] as String?,
      sessionId: json['sessionId'] as String?,
      permissions:
          UserPermissions.fromJson(json['permissions'] as Map<String, dynamic>),
      lastLogin: json['lastLogin'] == null
          ? null
          : DateTime.parse(json['lastLogin'] as String),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'vehicleId': instance.vehicleId,
      'vehicleName': instance.vehicleName,
      'sessionId': instance.sessionId,
      'permissions': instance.permissions,
      'lastLogin': instance.lastLogin?.toIso8601String(),
      'isActive': instance.isActive,
    };

UserPermissions _$UserPermissionsFromJson(Map<String, dynamic> json) =>
    UserPermissions(
      canCreateOrders: json['canCreateOrders'] as bool? ?? false,
      canManageRoutes: json['canManageRoutes'] as bool? ?? false,
      canViewReports: json['canViewReports'] as bool? ?? false,
      canManageCustomers: json['canManageCustomers'] as bool? ?? false,
      canManageVehicles: json['canManageVehicles'] as bool? ?? false,
    );

Map<String, dynamic> _$UserPermissionsToJson(UserPermissions instance) =>
    <String, dynamic>{
      'canCreateOrders': instance.canCreateOrders,
      'canManageRoutes': instance.canManageRoutes,
      'canViewReports': instance.canViewReports,
      'canManageCustomers': instance.canManageCustomers,
      'canManageVehicles': instance.canManageVehicles,
    };
