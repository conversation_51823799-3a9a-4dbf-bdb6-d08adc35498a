import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import 'storage_service.dart';

class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();
  ApiService._();

  late Dio _dio;
  String? _sessionId;

  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: kDebugMode,
      responseBody: kDebugMode,
      logPrint: (obj) => debugPrint(obj.toString()),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add session ID if available
        if (_sessionId != null) {
          options.headers['Cookie'] = 'session_id=$_sessionId';
        }
        handler.next(options);
      },
      onError: (error, handler) {
        _handleError(error);
        handler.next(error);
      },
    ));

    // Load saved session
    _loadSession();
  }

  void _loadSession() {
    _sessionId = StorageService.getString('session_id');
  }

  void _saveSession(String sessionId) {
    _sessionId = sessionId;
    StorageService.setString('session_id', sessionId);
  }

  void _clearSession() {
    _sessionId = null;
    StorageService.setString('session_id', '');
  }

  void _handleError(DioException error) {
    debugPrint('API Error: ${error.message}');
    
    if (error.response?.statusCode == 401) {
      // Unauthorized - clear session
      _clearSession();
    }
  }

  Future<ApiResponse<T>> _makeRequest<T>(
    String method,
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _dio.get(endpoint, queryParameters: queryParameters);
          break;
        case 'POST':
          response = await _dio.post(endpoint, data: data, queryParameters: queryParameters);
          break;
        case 'PUT':
          response = await _dio.put(endpoint, data: data, queryParameters: queryParameters);
          break;
        case 'DELETE':
          response = await _dio.delete(endpoint, queryParameters: queryParameters);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      final responseData = response.data as Map<String, dynamic>;
      
      if (responseData['success'] == true) {
        T? data;
        if (fromJson != null && responseData['data'] != null) {
          data = fromJson(responseData['data']);
        }
        
        return ApiResponse<T>(
          success: true,
          data: data,
          message: responseData['message'],
        );
      } else {
        return ApiResponse<T>(
          success: false,
          error: responseData['error'] ?? 'Unknown error',
        );
      }
    } on DioException catch (e) {
      String errorMessage = 'Network error';
      
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Receive timeout';
      } else if (e.type == DioExceptionType.badResponse) {
        errorMessage = 'Server error: ${e.response?.statusCode}';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'No internet connection';
      }

      return ApiResponse<T>(
        success: false,
        error: errorMessage,
      );
    } catch (e) {
      return ApiResponse<T>(
        success: false,
        error: 'Unexpected error: $e',
      );
    }
  }

  // Authentication
  Future<ApiResponse<Map<String, dynamic>>> login(String username, String password) async {
    final response = await _makeRequest<Map<String, dynamic>>(
      'POST',
      '/api/mobile/van/login',
      data: {
        'username': username,
        'password': password,
      },
      fromJson: (data) => data,
    );

    if (response.success && response.data != null) {
      final sessionId = response.data!['session_id'] as String?;
      if (sessionId != null) {
        _saveSession(sessionId);
      }
    }

    return response;
  }

  Future<ApiResponse<void>> logout() async {
    final response = await _makeRequest<void>('POST', '/api/van/logout');
    _clearSession();
    return response;
  }

  // Vehicle operations
  Future<ApiResponse<List<Map<String, dynamic>>>> getVehicles() async {
    return await _makeRequest<List<Map<String, dynamic>>>(
      'GET',
      '/api/van/vehicles',
      fromJson: (data) => List<Map<String, dynamic>>.from(data),
    );
  }

  Future<ApiResponse<void>> updateVehicleLocation(int vehicleId, double latitude, double longitude) async {
    return await _makeRequest<void>(
      'POST',
      '/api/van/vehicle/$vehicleId/location',
      data: {
        'latitude': latitude,
        'longitude': longitude,
      },
    );
  }

  // Route operations
  Future<ApiResponse<List<Map<String, dynamic>>>> getRoutes({
    String? dateFrom,
    String? dateTo,
    int? vehicleId,
    String? state,
  }) async {
    final queryParams = <String, dynamic>{};
    if (dateFrom != null) queryParams['date_from'] = dateFrom;
    if (dateTo != null) queryParams['date_to'] = dateTo;
    if (vehicleId != null) queryParams['vehicle_id'] = vehicleId.toString();
    if (state != null) queryParams['state'] = state;

    return await _makeRequest<List<Map<String, dynamic>>>(
      'GET',
      '/api/van/routes',
      queryParameters: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data),
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> getRouteDetails(int routeId) async {
    return await _makeRequest<Map<String, dynamic>>(
      'GET',
      '/api/van/route/$routeId',
      fromJson: (data) => data,
    );
  }

  Future<ApiResponse<void>> optimizeRoute(int routeId) async {
    return await _makeRequest<void>('POST', '/api/van/route/$routeId/optimize');
  }

  // Customer operations
  Future<ApiResponse<List<Map<String, dynamic>>>> getCustomers({
    int? territoryId,
    String? search,
    int? limit,
  }) async {
    final queryParams = <String, dynamic>{};
    if (territoryId != null) queryParams['territory_id'] = territoryId.toString();
    if (search != null) queryParams['search'] = search;
    if (limit != null) queryParams['limit'] = limit.toString();

    return await _makeRequest<List<Map<String, dynamic>>>(
      'GET',
      '/api/van/customers',
      queryParameters: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data),
    );
  }

  // Territory operations
  Future<ApiResponse<List<Map<String, dynamic>>>> getTerritories() async {
    return await _makeRequest<List<Map<String, dynamic>>>(
      'GET',
      '/api/van/territories',
      fromJson: (data) => List<Map<String, dynamic>>.from(data),
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> validateTerritoryLocation(
    int territoryId,
    double latitude,
    double longitude,
  ) async {
    return await _makeRequest<Map<String, dynamic>>(
      'POST',
      '/api/van/territory/$territoryId/validate',
      data: {
        'latitude': latitude,
        'longitude': longitude,
      },
      fromJson: (data) => data,
    );
  }

  // Mobile sync
  Future<ApiResponse<Map<String, dynamic>>> syncData() async {
    return await _makeRequest<Map<String, dynamic>>(
      'GET',
      '/api/mobile/van/sync',
      fromJson: (data) => data,
    );
  }

  // Visit operations
  Future<ApiResponse<void>> startVisit(int visitId, {double? latitude, double? longitude}) async {
    final data = <String, dynamic>{};
    if (latitude != null) data['latitude'] = latitude;
    if (longitude != null) data['longitude'] = longitude;

    return await _makeRequest<void>('POST', '/api/mobile/van/visit/$visitId/start', data: data);
  }

  Future<ApiResponse<void>> completeVisit(
    int visitId, {
    String? notes,
    String? customerFeedback,
    String? visitResult,
    String? nextVisitDate,
    bool? followUpRequired,
    String? followUpNotes,
  }) async {
    final data = <String, dynamic>{};
    if (notes != null) data['notes'] = notes;
    if (customerFeedback != null) data['customer_feedback'] = customerFeedback;
    if (visitResult != null) data['visit_result'] = visitResult;
    if (nextVisitDate != null) data['next_visit_date'] = nextVisitDate;
    if (followUpRequired != null) data['follow_up_required'] = followUpRequired;
    if (followUpNotes != null) data['follow_up_notes'] = followUpNotes;

    return await _makeRequest<void>('POST', '/api/mobile/van/visit/$visitId/complete', data: data);
  }

  // Order operations
  Future<ApiResponse<Map<String, dynamic>>> createOrder({
    required int customerId,
    int? visitId,
    String? deliveryMethod,
    String? paymentMethod,
    double? latitude,
    double? longitude,
    required List<Map<String, dynamic>> orderLines,
  }) async {
    final data = {
      'customer_id': customerId,
      'order_lines': orderLines,
    };

    if (visitId != null) data['visit_id'] = visitId;
    if (deliveryMethod != null) data['delivery_method'] = deliveryMethod;
    if (paymentMethod != null) data['payment_method'] = paymentMethod;
    if (latitude != null) data['latitude'] = latitude;
    if (longitude != null) data['longitude'] = longitude;

    return await _makeRequest<Map<String, dynamic>>(
      'POST',
      '/api/mobile/van/order/create',
      data: data,
      fromJson: (data) => data,
    );
  }

  bool get isAuthenticated => _sessionId != null && _sessionId!.isNotEmpty;
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final String? message;

  ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.message,
  });

  @override
  String toString() {
    return 'ApiResponse{success: $success, data: $data, error: $error, message: $message}';
  }
}
