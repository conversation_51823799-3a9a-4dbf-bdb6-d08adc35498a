// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'route.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VanRouteAdapter extends TypeAdapter<VanRoute> {
  @override
  final int typeId = 4;

  @override
  VanRoute read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VanRoute(
      id: fields[0] as int,
      name: fields[1] as String,
      code: fields[2] as String,
      plannedDate: fields[3] as DateTime,
      state: fields[4] as RouteState,
      vehicleId: fields[5] as int,
      vehicleName: fields[6] as String,
      driverName: fields[7] as String?,
      startLocation: fields[8] as String?,
      startLatitude: fields[9] as double?,
      startLongitude: fields[10] as double?,
      endLocation: fields[11] as String?,
      endLatitude: fields[12] as double?,
      endLongitude: fields[13] as double?,
      totalDistance: fields[14] as double,
      estimatedDuration: fields[15] as double,
      actualDistance: fields[16] as double?,
      actualDuration: fields[17] as double?,
      visits: (fields[18] as List).cast<Visit>(),
      notes: fields[19] as String?,
      specialInstructions: fields[20] as String?,
      startTime: fields[21] as DateTime?,
      endTime: fields[22] as DateTime?,
      optimizedRoute: (fields[23] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, VanRoute obj) {
    writer
      ..writeByte(24)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.code)
      ..writeByte(3)
      ..write(obj.plannedDate)
      ..writeByte(4)
      ..write(obj.state)
      ..writeByte(5)
      ..write(obj.vehicleId)
      ..writeByte(6)
      ..write(obj.vehicleName)
      ..writeByte(7)
      ..write(obj.driverName)
      ..writeByte(8)
      ..write(obj.startLocation)
      ..writeByte(9)
      ..write(obj.startLatitude)
      ..writeByte(10)
      ..write(obj.startLongitude)
      ..writeByte(11)
      ..write(obj.endLocation)
      ..writeByte(12)
      ..write(obj.endLatitude)
      ..writeByte(13)
      ..write(obj.endLongitude)
      ..writeByte(14)
      ..write(obj.totalDistance)
      ..writeByte(15)
      ..write(obj.estimatedDuration)
      ..writeByte(16)
      ..write(obj.actualDistance)
      ..writeByte(17)
      ..write(obj.actualDuration)
      ..writeByte(18)
      ..write(obj.visits)
      ..writeByte(19)
      ..write(obj.notes)
      ..writeByte(20)
      ..write(obj.specialInstructions)
      ..writeByte(21)
      ..write(obj.startTime)
      ..writeByte(22)
      ..write(obj.endTime)
      ..writeByte(23)
      ..write(obj.optimizedRoute);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VanRouteAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RouteStateAdapter extends TypeAdapter<RouteState> {
  @override
  final int typeId = 5;

  @override
  RouteState read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RouteState.draft;
      case 1:
        return RouteState.planned;
      case 2:
        return RouteState.inProgress;
      case 3:
        return RouteState.completed;
      case 4:
        return RouteState.cancelled;
      default:
        return RouteState.draft;
    }
  }

  @override
  void write(BinaryWriter writer, RouteState obj) {
    switch (obj) {
      case RouteState.draft:
        writer.writeByte(0);
        break;
      case RouteState.planned:
        writer.writeByte(1);
        break;
      case RouteState.inProgress:
        writer.writeByte(2);
        break;
      case RouteState.completed:
        writer.writeByte(3);
        break;
      case RouteState.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RouteStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VanRoute _$VanRouteFromJson(Map<String, dynamic> json) => VanRoute(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      code: json['code'] as String,
      plannedDate: DateTime.parse(json['plannedDate'] as String),
      state: $enumDecodeNullable(_$RouteStateEnumMap, json['state']) ??
          RouteState.draft,
      vehicleId: (json['vehicleId'] as num).toInt(),
      vehicleName: json['vehicleName'] as String,
      driverName: json['driverName'] as String?,
      startLocation: json['startLocation'] as String?,
      startLatitude: (json['startLatitude'] as num?)?.toDouble(),
      startLongitude: (json['startLongitude'] as num?)?.toDouble(),
      endLocation: json['endLocation'] as String?,
      endLatitude: (json['endLatitude'] as num?)?.toDouble(),
      endLongitude: (json['endLongitude'] as num?)?.toDouble(),
      totalDistance: (json['totalDistance'] as num?)?.toDouble() ?? 0.0,
      estimatedDuration: (json['estimatedDuration'] as num?)?.toDouble() ?? 0.0,
      actualDistance: (json['actualDistance'] as num?)?.toDouble(),
      actualDuration: (json['actualDuration'] as num?)?.toDouble(),
      visits: (json['visits'] as List<dynamic>?)
              ?.map((e) => Visit.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      notes: json['notes'] as String?,
      specialInstructions: json['specialInstructions'] as String?,
      startTime: json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      optimizedRoute: json['optimizedRoute'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$VanRouteToJson(VanRoute instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'plannedDate': instance.plannedDate.toIso8601String(),
      'state': _$RouteStateEnumMap[instance.state]!,
      'vehicleId': instance.vehicleId,
      'vehicleName': instance.vehicleName,
      'driverName': instance.driverName,
      'startLocation': instance.startLocation,
      'startLatitude': instance.startLatitude,
      'startLongitude': instance.startLongitude,
      'endLocation': instance.endLocation,
      'endLatitude': instance.endLatitude,
      'endLongitude': instance.endLongitude,
      'totalDistance': instance.totalDistance,
      'estimatedDuration': instance.estimatedDuration,
      'actualDistance': instance.actualDistance,
      'actualDuration': instance.actualDuration,
      'visits': instance.visits,
      'notes': instance.notes,
      'specialInstructions': instance.specialInstructions,
      'startTime': instance.startTime?.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'optimizedRoute': instance.optimizedRoute,
    };

const _$RouteStateEnumMap = {
  RouteState.draft: 'draft',
  RouteState.planned: 'planned',
  RouteState.inProgress: 'inProgress',
  RouteState.completed: 'completed',
  RouteState.cancelled: 'cancelled',
};
