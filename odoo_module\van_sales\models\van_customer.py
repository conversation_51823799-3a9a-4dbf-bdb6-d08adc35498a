from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class VanCustomer(models.Model):
    _name = 'van.customer'
    _description = 'Van Sales Customer'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Customer Name',
        required=True,
        tracking=True
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='Related Partner',
        required=True,
        ondelete='cascade'
    )
    
    # Location Information
    latitude = fields.Float(
        string='Latitude',
        digits=(10, 6),
        required=True
    )
    longitude = fields.Float(
        string='Longitude',
        digits=(10, 6),
        required=True
    )
    address = fields.Text(
        string='Address',
        related='partner_id.contact_address_complete',
        readonly=True
    )
    
    # Territory and Route Information
    territory_id = fields.Many2one(
        'van.territory',
        string='Territory',
        compute='_compute_territory',
        store=True
    )
    preferred_visit_time = fields.Selection([
        ('morning', 'Morning (8:00-12:00)'),
        ('afternoon', 'Afternoon (12:00-17:00)'),
        ('evening', 'Evening (17:00-20:00)'),
        ('anytime', 'Anytime')
    ], string='Preferred Visit Time', default='anytime')
    
    visit_frequency = fields.Selection([
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('biweekly', 'Bi-weekly'),
        ('monthly', 'Monthly'),
        ('on_demand', 'On Demand')
    ], string='Visit Frequency', default='weekly')
    
    # Customer Classification
    customer_type = fields.Selection([
        ('retail', 'Retail'),
        ('wholesale', 'Wholesale'),
        ('distributor', 'Distributor'),
        ('restaurant', 'Restaurant'),
        ('supermarket', 'Supermarket')
    ], string='Customer Type', default='retail')
    
    priority = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ], string='Priority', default='medium')
    
    # Status and Configuration
    active = fields.Boolean(
        string='Active',
        default=True
    )
    is_van_customer = fields.Boolean(
        string='Van Sales Customer',
        default=True
    )
    
    # Sales Information
    credit_limit = fields.Monetary(
        string='Credit Limit',
        currency_field='currency_id'
    )
    payment_terms_id = fields.Many2one(
        'account.payment.term',
        string='Payment Terms'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Visits and Orders
    visit_ids = fields.One2many(
        'van.visit',
        'customer_id',
        string='Visits'
    )
    sales_order_ids = fields.One2many(
        'sale.order',
        'partner_id',
        string='Sales Orders',
        domain=[('van_sales_order', '=', True)]
    )
    
    # Statistics
    total_visits = fields.Integer(
        string='Total Visits',
        compute='_compute_statistics'
    )
    last_visit_date = fields.Datetime(
        string='Last Visit Date',
        compute='_compute_statistics'
    )
    total_sales = fields.Monetary(
        string='Total Sales',
        compute='_compute_statistics',
        currency_field='currency_id'
    )
    average_order_value = fields.Monetary(
        string='Average Order Value',
        compute='_compute_statistics',
        currency_field='currency_id'
    )
    
    # Special Requirements
    delivery_instructions = fields.Text(
        string='Delivery Instructions'
    )
    access_requirements = fields.Text(
        string='Access Requirements',
        help='Special requirements for accessing the customer location'
    )
    contact_person = fields.Char(
        string='Contact Person'
    )
    contact_phone = fields.Char(
        string='Contact Phone'
    )
    
    @api.depends('latitude', 'longitude')
    def _compute_territory(self):
        """Automatically assign territory based on customer location"""
        for customer in self:
            if customer.latitude and customer.longitude:
                territories = self.env['van.territory'].search([('active', '=', True)])
                for territory in territories:
                    if territory.is_point_in_territory(customer.latitude, customer.longitude):
                        customer.territory_id = territory.id
                        break
                else:
                    customer.territory_id = False
            else:
                customer.territory_id = False
    
    @api.depends('visit_ids', 'sales_order_ids')
    def _compute_statistics(self):
        for customer in self:
            # Visit statistics
            customer.total_visits = len(customer.visit_ids)
            if customer.visit_ids:
                customer.last_visit_date = max(customer.visit_ids.mapped('visit_date'))
            else:
                customer.last_visit_date = False
            
            # Sales statistics
            confirmed_orders = customer.sales_order_ids.filtered(
                lambda o: o.state in ['sale', 'done']
            )
            customer.total_sales = sum(confirmed_orders.mapped('amount_total'))
            if confirmed_orders:
                customer.average_order_value = customer.total_sales / len(confirmed_orders)
            else:
                customer.average_order_value = 0.0
    
    @api.constrains('latitude', 'longitude')
    def _check_coordinates(self):
        for customer in self:
            if not (-90 <= customer.latitude <= 90):
                raise ValidationError(_('Latitude must be between -90 and 90 degrees.'))
            if not (-180 <= customer.longitude <= 180):
                raise ValidationError(_('Longitude must be between -180 and 180 degrees.'))
    
    @api.model
    def create(self, vals):
        """Override create to ensure partner relationship"""
        if 'partner_id' not in vals and 'name' in vals:
            # Create partner if not provided
            partner = self.env['res.partner'].create({
                'name': vals['name'],
                'is_company': True,
                'customer_rank': 1,
                'van_customer': True
            })
            vals['partner_id'] = partner.id
        
        customer = super().create(vals)
        
        # Update partner with location information
        if customer.partner_id:
            customer.partner_id.write({
                'van_latitude': customer.latitude,
                'van_longitude': customer.longitude,
                'van_territory_id': customer.territory_id.id if customer.territory_id else False
            })
        
        return customer
    
    def write(self, vals):
        """Override write to sync with partner"""
        result = super().write(vals)
        
        # Sync location changes with partner
        if any(field in vals for field in ['latitude', 'longitude', 'territory_id']):
            for customer in self:
                if customer.partner_id:
                    partner_vals = {}
                    if 'latitude' in vals:
                        partner_vals['van_latitude'] = customer.latitude
                    if 'longitude' in vals:
                        partner_vals['van_longitude'] = customer.longitude
                    if 'territory_id' in vals:
                        partner_vals['van_territory_id'] = customer.territory_id.id if customer.territory_id else False
                    
                    if partner_vals:
                        customer.partner_id.write(partner_vals)
        
        return result
    
    def action_schedule_visit(self):
        """Open wizard to schedule a visit"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Schedule Visit'),
            'res_model': 'van.visit.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_customer_id': self.id,
                'default_partner_id': self.partner_id.id
            }
        }
    
    def action_view_visits(self):
        """View customer visits"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Customer Visits'),
            'res_model': 'van.visit',
            'view_mode': 'tree,form',
            'domain': [('customer_id', '=', self.id)],
            'context': {'default_customer_id': self.id}
        }
    
    def action_view_orders(self):
        """View customer orders"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Customer Orders'),
            'res_model': 'sale.order',
            'view_mode': 'tree,form',
            'domain': [('partner_id', '=', self.partner_id.id)],
            'context': {'default_partner_id': self.partner_id.id}
        }
