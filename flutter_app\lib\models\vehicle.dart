import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'vehicle.g.dart';

@HiveType(typeId: 2)
@JsonSerializable()
class Vehicle {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String licensePlate;

  @HiveField(3)
  final int? driverId;

  @HiveField(4)
  final String? driverName;

  @HiveField(5)
  final VehicleState state;

  @HiveField(6)
  final double? currentLatitude;

  @HiveField(7)
  final double? currentLongitude;

  @HiveField(8)
  final DateTime? lastLocationUpdate;

  @HiveField(9)
  final double? maxWeightCapacity;

  @HiveField(10)
  final double? maxVolumeCapacity;

  @HiveField(11)
  final String? fuelType;

  @HiveField(12)
  final bool isActive;

  const Vehicle({
    required this.id,
    required this.name,
    required this.licensePlate,
    this.driverId,
    this.driverName,
    this.state = VehicleState.available,
    this.currentLatitude,
    this.currentLongitude,
    this.lastLocationUpdate,
    this.maxWeightCapacity,
    this.maxVolumeCapacity,
    this.fuelType,
    this.isActive = true,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) => _$VehicleFromJson(json);
  Map<String, dynamic> toJson() => _$VehicleToJson(this);

  Vehicle copyWith({
    int? id,
    String? name,
    String? licensePlate,
    int? driverId,
    String? driverName,
    VehicleState? state,
    double? currentLatitude,
    double? currentLongitude,
    DateTime? lastLocationUpdate,
    double? maxWeightCapacity,
    double? maxVolumeCapacity,
    String? fuelType,
    bool? isActive,
  }) {
    return Vehicle(
      id: id ?? this.id,
      name: name ?? this.name,
      licensePlate: licensePlate ?? this.licensePlate,
      driverId: driverId ?? this.driverId,
      driverName: driverName ?? this.driverName,
      state: state ?? this.state,
      currentLatitude: currentLatitude ?? this.currentLatitude,
      currentLongitude: currentLongitude ?? this.currentLongitude,
      lastLocationUpdate: lastLocationUpdate ?? this.lastLocationUpdate,
      maxWeightCapacity: maxWeightCapacity ?? this.maxWeightCapacity,
      maxVolumeCapacity: maxVolumeCapacity ?? this.maxVolumeCapacity,
      fuelType: fuelType ?? this.fuelType,
      isActive: isActive ?? this.isActive,
    );
  }

  bool get hasLocation => currentLatitude != null && currentLongitude != null;

  String get stateDisplayName {
    switch (state) {
      case VehicleState.available:
        return 'Available';
      case VehicleState.onRoute:
        return 'On Route';
      case VehicleState.maintenance:
        return 'Maintenance';
      case VehicleState.inactive:
        return 'Inactive';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Vehicle && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Vehicle{id: $id, name: $name, licensePlate: $licensePlate, state: $state}';
  }
}

@HiveType(typeId: 3)
enum VehicleState {
  @HiveField(0)
  available,
  @HiveField(1)
  onRoute,
  @HiveField(2)
  maintenance,
  @HiveField(3)
  inactive,
}
