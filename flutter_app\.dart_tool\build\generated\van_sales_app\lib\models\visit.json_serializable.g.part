// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Visit _$VisitFromJson(Map<String, dynamic> json) => Visit(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      sequence: (json['sequence'] as num?)?.toInt() ?? 0,
      customerId: (json['customerId'] as num).toInt(),
      customerName: json['customerName'] as String,
      customerLatitude: (json['customerLatitude'] as num?)?.toDouble(),
      customerLongitude: (json['customerLongitude'] as num?)?.toDouble(),
      visitDate: DateTime.parse(json['visitDate'] as String),
      plannedTime: json['plannedTime'] == null
          ? null
          : DateTime.parse(json['plannedTime'] as String),
      state: $enumDecodeNullable(_$VisitStateEnumMap, json['state']) ??
          VisitState.planned,
      visitType: $enumDecodeNullable(_$VisitTypeEnumMap, json['visitType']) ??
          VisitType.sales,
      purpose: json['purpose'] as String?,
      notes: json['notes'] as String?,
      actualArrivalTime: json['actualArrivalTime'] == null
          ? null
          : DateTime.parse(json['actualArrivalTime'] as String),
      actualDepartureTime: json['actualDepartureTime'] == null
          ? null
          : DateTime.parse(json['actualDepartureTime'] as String),
      actualLatitude: (json['actualLatitude'] as num?)?.toDouble(),
      actualLongitude: (json['actualLongitude'] as num?)?.toDouble(),
      visitResult:
          $enumDecodeNullable(_$VisitResultEnumMap, json['visitResult']),
      customerFeedback: json['customerFeedback'] as String?,
      nextVisitDate: json['nextVisitDate'] == null
          ? null
          : DateTime.parse(json['nextVisitDate'] as String),
      followUpRequired: json['followUpRequired'] as bool? ?? false,
      followUpNotes: json['followUpNotes'] as String?,
      totalSales: (json['totalSales'] as num?)?.toDouble() ?? 0.0,
      orderCount: (json['orderCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$VisitToJson(Visit instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'sequence': instance.sequence,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'customerLatitude': instance.customerLatitude,
      'customerLongitude': instance.customerLongitude,
      'visitDate': instance.visitDate.toIso8601String(),
      'plannedTime': instance.plannedTime?.toIso8601String(),
      'state': _$VisitStateEnumMap[instance.state]!,
      'visitType': _$VisitTypeEnumMap[instance.visitType]!,
      'purpose': instance.purpose,
      'notes': instance.notes,
      'actualArrivalTime': instance.actualArrivalTime?.toIso8601String(),
      'actualDepartureTime': instance.actualDepartureTime?.toIso8601String(),
      'actualLatitude': instance.actualLatitude,
      'actualLongitude': instance.actualLongitude,
      'visitResult': _$VisitResultEnumMap[instance.visitResult],
      'customerFeedback': instance.customerFeedback,
      'nextVisitDate': instance.nextVisitDate?.toIso8601String(),
      'followUpRequired': instance.followUpRequired,
      'followUpNotes': instance.followUpNotes,
      'totalSales': instance.totalSales,
      'orderCount': instance.orderCount,
    };

const _$VisitStateEnumMap = {
  VisitState.planned: 'planned',
  VisitState.inProgress: 'inProgress',
  VisitState.completed: 'completed',
  VisitState.cancelled: 'cancelled',
  VisitState.noShow: 'noShow',
};

const _$VisitTypeEnumMap = {
  VisitType.sales: 'sales',
  VisitType.delivery: 'delivery',
  VisitType.collection: 'collection',
  VisitType.service: 'service',
  VisitType.survey: 'survey',
  VisitType.maintenance: 'maintenance',
};

const _$VisitResultEnumMap = {
  VisitResult.successful: 'successful',
  VisitResult.partial: 'partial',
  VisitResult.unsuccessful: 'unsuccessful',
  VisitResult.rescheduled: 'rescheduled',
};
