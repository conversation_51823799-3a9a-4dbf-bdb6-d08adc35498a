import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/auth_provider.dart';
import '../../screens/auth/login_screen.dart';
import '../../screens/auth/splash_screen.dart';
import '../../screens/dashboard/dashboard_screen.dart';
import '../../screens/routes/routes_screen.dart';
import '../../screens/routes/route_detail_screen.dart';
import '../../screens/customers/customers_screen.dart';
import '../../screens/customers/customer_detail_screen.dart';
import '../../screens/visits/visits_screen.dart';
import '../../screens/visits/visit_detail_screen.dart';
import '../../screens/map/map_screen.dart';
import '../../screens/settings/settings_screen.dart';
import '../../screens/profile/profile_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) {
      final isAuthenticated = authState.isAuthenticated;
      final isLoading = authState.isLoading;
      final location = state.matchedLocation;

      // Show splash while loading
      if (isLoading && location == '/splash') {
        return null;
      }

      // Redirect to login if not authenticated
      if (!isAuthenticated && location != '/login' && location != '/splash') {
        return '/login';
      }

      // Redirect to dashboard if authenticated and on login/splash
      if (isAuthenticated && (location == '/login' || location == '/splash')) {
        return '/dashboard';
      }

      return null;
    },
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Authentication
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      // Main App Shell with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          // Dashboard
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),

          // Routes
          GoRoute(
            path: '/routes',
            name: 'routes',
            builder: (context, state) => const RoutesScreen(),
            routes: [
              GoRoute(
                path: ':routeId',
                name: 'route-detail',
                builder: (context, state) {
                  final routeId = int.parse(state.pathParameters['routeId']!);
                  return RouteDetailScreen(routeId: routeId);
                },
              ),
            ],
          ),

          // Customers
          GoRoute(
            path: '/customers',
            name: 'customers',
            builder: (context, state) => const CustomersScreen(),
            routes: [
              GoRoute(
                path: ':customerId',
                name: 'customer-detail',
                builder: (context, state) {
                  final customerId = int.parse(state.pathParameters['customerId']!);
                  return CustomerDetailScreen(customerId: customerId);
                },
              ),
            ],
          ),

          // Visits
          GoRoute(
            path: '/visits',
            name: 'visits',
            builder: (context, state) => const VisitsScreen(),
            routes: [
              GoRoute(
                path: ':visitId',
                name: 'visit-detail',
                builder: (context, state) {
                  final visitId = int.parse(state.pathParameters['visitId']!);
                  return VisitDetailScreen(visitId: visitId);
                },
              ),
            ],
          ),

          // Map
          GoRoute(
            path: '/map',
            name: 'map',
            builder: (context, state) => const MapScreen(),
          ),
        ],
      ),

      // Settings (Full Screen)
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),

      // Profile (Full Screen)
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

class MainShell extends StatefulWidget {
  final Widget child;

  const MainShell({super.key, required this.child});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  int _selectedIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.dashboard,
      label: 'Dashboard',
      route: '/dashboard',
    ),
    NavigationItem(
      icon: Icons.route,
      label: 'Routes',
      route: '/routes',
    ),
    NavigationItem(
      icon: Icons.people,
      label: 'Customers',
      route: '/customers',
    ),
    NavigationItem(
      icon: Icons.assignment,
      label: 'Visits',
      route: '/visits',
    ),
    NavigationItem(
      icon: Icons.map,
      label: 'Map',
      route: '/map',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
          context.go(_navigationItems[index].route);
        },
        items: _navigationItems.map((item) {
          return BottomNavigationBarItem(
            icon: Icon(item.icon),
            label: item.label,
          );
        }).toList(),
      ),
    );
  }
}

class NavigationItem {
  final IconData icon;
  final String label;
  final String route;

  NavigationItem({
    required this.icon,
    required this.label,
    required this.route,
  });
}
