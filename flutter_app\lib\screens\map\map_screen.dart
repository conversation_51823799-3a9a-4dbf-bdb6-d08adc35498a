import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class MapScreen extends ConsumerWidget {
  const MapScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Map'),
        actions: [
          IconButton(icon: const Icon(Icons.my_location), onPressed: () {}),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.map, size: 64, color: AppTheme.territoryColor),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            Text('Map Screen', style: AppTheme.headingMedium),
            SizedBox(height: 8),
            Text('Interactive map coming soon', style: AppTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
