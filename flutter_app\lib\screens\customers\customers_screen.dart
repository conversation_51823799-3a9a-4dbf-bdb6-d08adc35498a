import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_theme.dart';

class CustomersScreen extends ConsumerWidget {
  const CustomersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customers'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {},
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people, size: 64, color: AppTheme.customerColor),
            Si<PERSON><PERSON><PERSON>(height: 16),
            Text('Customers Screen', style: AppTheme.headingMedium),
            SizedBox(height: 8),
            Text('Customer management coming soon', style: AppTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
