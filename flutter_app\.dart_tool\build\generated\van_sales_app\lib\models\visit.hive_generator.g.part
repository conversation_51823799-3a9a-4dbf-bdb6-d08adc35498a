// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VisitAdapter extends TypeAdapter<Visit> {
  @override
  final int typeId = 11;

  @override
  Visit read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Visit(
      id: fields[0] as int,
      name: fields[1] as String,
      sequence: fields[2] as int,
      customerId: fields[3] as int,
      customerName: fields[4] as String,
      customerLatitude: fields[5] as double?,
      customerLongitude: fields[6] as double?,
      visitDate: fields[7] as DateTime,
      plannedTime: fields[8] as DateTime?,
      state: fields[9] as VisitState,
      visitType: fields[10] as VisitType,
      purpose: fields[11] as String?,
      notes: fields[12] as String?,
      actualArrivalTime: fields[13] as DateTime?,
      actualDepartureTime: fields[14] as DateTime?,
      actualLatitude: fields[15] as double?,
      actualLongitude: fields[16] as double?,
      visitResult: fields[17] as VisitResult?,
      customerFeedback: fields[18] as String?,
      nextVisitDate: fields[19] as DateTime?,
      followUpRequired: fields[20] as bool,
      followUpNotes: fields[21] as String?,
      totalSales: fields[22] as double,
      orderCount: fields[23] as int,
    );
  }

  @override
  void write(BinaryWriter writer, Visit obj) {
    writer
      ..writeByte(24)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.sequence)
      ..writeByte(3)
      ..write(obj.customerId)
      ..writeByte(4)
      ..write(obj.customerName)
      ..writeByte(5)
      ..write(obj.customerLatitude)
      ..writeByte(6)
      ..write(obj.customerLongitude)
      ..writeByte(7)
      ..write(obj.visitDate)
      ..writeByte(8)
      ..write(obj.plannedTime)
      ..writeByte(9)
      ..write(obj.state)
      ..writeByte(10)
      ..write(obj.visitType)
      ..writeByte(11)
      ..write(obj.purpose)
      ..writeByte(12)
      ..write(obj.notes)
      ..writeByte(13)
      ..write(obj.actualArrivalTime)
      ..writeByte(14)
      ..write(obj.actualDepartureTime)
      ..writeByte(15)
      ..write(obj.actualLatitude)
      ..writeByte(16)
      ..write(obj.actualLongitude)
      ..writeByte(17)
      ..write(obj.visitResult)
      ..writeByte(18)
      ..write(obj.customerFeedback)
      ..writeByte(19)
      ..write(obj.nextVisitDate)
      ..writeByte(20)
      ..write(obj.followUpRequired)
      ..writeByte(21)
      ..write(obj.followUpNotes)
      ..writeByte(22)
      ..write(obj.totalSales)
      ..writeByte(23)
      ..write(obj.orderCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisitAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VisitStateAdapter extends TypeAdapter<VisitState> {
  @override
  final int typeId = 12;

  @override
  VisitState read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VisitState.planned;
      case 1:
        return VisitState.inProgress;
      case 2:
        return VisitState.completed;
      case 3:
        return VisitState.cancelled;
      case 4:
        return VisitState.noShow;
      default:
        return VisitState.planned;
    }
  }

  @override
  void write(BinaryWriter writer, VisitState obj) {
    switch (obj) {
      case VisitState.planned:
        writer.writeByte(0);
        break;
      case VisitState.inProgress:
        writer.writeByte(1);
        break;
      case VisitState.completed:
        writer.writeByte(2);
        break;
      case VisitState.cancelled:
        writer.writeByte(3);
        break;
      case VisitState.noShow:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisitStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VisitTypeAdapter extends TypeAdapter<VisitType> {
  @override
  final int typeId = 13;

  @override
  VisitType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VisitType.sales;
      case 1:
        return VisitType.delivery;
      case 2:
        return VisitType.collection;
      case 3:
        return VisitType.service;
      case 4:
        return VisitType.survey;
      case 5:
        return VisitType.maintenance;
      default:
        return VisitType.sales;
    }
  }

  @override
  void write(BinaryWriter writer, VisitType obj) {
    switch (obj) {
      case VisitType.sales:
        writer.writeByte(0);
        break;
      case VisitType.delivery:
        writer.writeByte(1);
        break;
      case VisitType.collection:
        writer.writeByte(2);
        break;
      case VisitType.service:
        writer.writeByte(3);
        break;
      case VisitType.survey:
        writer.writeByte(4);
        break;
      case VisitType.maintenance:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisitTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VisitResultAdapter extends TypeAdapter<VisitResult> {
  @override
  final int typeId = 14;

  @override
  VisitResult read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VisitResult.successful;
      case 1:
        return VisitResult.partial;
      case 2:
        return VisitResult.unsuccessful;
      case 3:
        return VisitResult.rescheduled;
      default:
        return VisitResult.successful;
    }
  }

  @override
  void write(BinaryWriter writer, VisitResult obj) {
    switch (obj) {
      case VisitResult.successful:
        writer.writeByte(0);
        break;
      case VisitResult.partial:
        writer.writeByte(1);
        break;
      case VisitResult.unsuccessful:
        writer.writeByte(2);
        break;
      case VisitResult.rescheduled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisitResultAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
